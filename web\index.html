<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Weaver - Professional MIDI Editor</title>
    <link rel="stylesheet" href="/static/style.css?v=13" preload>
    <!-- Core Libraries - Optimized Loading -->
    <script src="https://cdn.jsdelivr.net/npm/rete@1.4.5/build/rete.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-connection-plugin@0.9.0/build/connection-plugin.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-area-plugin@0.2.1/build/area-plugin.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js" defer></script>
</head>
<body>
    <div id="app">
        <div class="toolbar">
            <h1>Pat<PERSON> Weaver</h1>

            <!-- Global Project Settings -->
            <div class="global-settings">
                <div class="setting-group">
                    <label>BPM:</label>
                    <input type="number"
                           v-model.number="globalBPM"
                           min="1"
                           max="65536"
                           class="bpm-input"
                           @change="updateGlobalBPM">
                </div>
                <div class="setting-group">
                    <label>PPQ:</label>
                    <input type="number"
                           v-model.number="globalPPQ"
                           min="96"
                           max="65536"
                           class="ppq-input"
                           @change="updateGlobalPPQ">
                </div>
            </div>

            <div class="toolbar-buttons">
                <div class="button-group">
                    <label>File:</label>
                    <button @click="showSaveAsMenu">Save As...</button>
                    <button @click="clearGraph">Clear</button>
                </div>

                <div class="button-group">
                    <label>Transport:</label>
                    <button @click="playSequence" :disabled="isPlaying">▶ Play</button>
                    <button @click="pauseSequence" :disabled="!isPlaying">⏸ Pause</button>
                    <button @click="stopSequence">⏹ Stop</button>
                </div>
            </div>
        </div>
        <div class="main-content">
            <!-- Sidebar hidden by default, controlled by floating menu -->
            <div class="sidebar" :class="{ 'sidebar-hidden': !showSidebar }">
                <div class="sidebar-header">
                    <span v-if="sidebarMode === 'nodes'">Node Library</span>
                    <span v-if="sidebarMode === 'settings'">Program Settings</span>
                    <span v-if="sidebarMode === 'files'">File Explorer</span>
                    <button class="sidebar-close" @click="closeSidebar">×</button>
                </div>
                <div class="sidebar-content">
                    <!-- Node Library Tab -->
                    <div v-if="sidebarMode === 'nodes'" class="node-category">
                        <h4>Available Nodes</h4>
                        <div class="node-list">
                            <!-- MIDI Processing Nodes -->
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Load', 'midiLoad')"
                                 @click="addMidiLoadNode">
                                <span class="node-icon">🎵</span>
                                <span>MIDI Load</span>
                            </div>
                        </div>
                    </div>

                    <!-- Program Settings Tab -->
                    <div v-if="sidebarMode === 'settings'" class="settings-panel">
                        <div class="setting-section">
                            <h4>Audio Settings</h4>
                            <div class="setting-item">
                                <label>Synthesizer:</label>
                                <select v-model="selectedSynthesizer" @change="updateSynthesizer">
                                    <option value="winmm">Windows GS Wavetable (WinMM)</option>
                                    <option value="kdmapi">KDMAPI Synthesizer</option>
                                </select>
                            </div>
                        </div>

                        <div class="setting-section">
                            <h4>Appearance</h4>
                            <div class="setting-item">
                                <label>Primary Color:</label>
                                <input type="color" v-model="primaryColor" @change="updateColorPalette">
                            </div>
                            <div class="setting-item">
                                <label>Secondary Color:</label>
                                <input type="color" v-model="secondaryColor" @change="updateColorPalette">
                            </div>
                            <button @click="resetColors" class="reset-btn">Reset to Default</button>
                        </div>
                    </div>

                    <!-- File Explorer Tab -->
                    <div v-if="sidebarMode === 'files'" class="file-explorer">
                        <div class="file-section">
                            <h4>Load Files</h4>
                            <button @click="loadWeaveFile" class="file-btn">📁 Load WEAVE Project</button>
                        </div>

                        <div class="file-section">
                            <h4>Recent Files</h4>
                            <div class="recent-files">
                                <p style="color: #9ca3af; font-size: 12px; text-align: center; padding: 10px;">
                                    No recent files
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="editor"
                 @dragover.prevent
                 @drop="handleDrop($event)">
                <!-- SVG Gradients for connections -->
                <svg style="position: absolute; width: 0; height: 0;">
                    <defs>
                        <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="selectedConnectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                </svg>
            </div>
        </div>

        <!-- Floating Menu Button -->
        <div class="floating-menu">
            <button class="floating-btn" @click="toggleFloatingMenu" :class="{ 'active': showFloatingMenu }">
                ☰
            </button>
            <div class="floating-menu-options" v-show="showFloatingMenu">
                <button @click="openSidebar('nodes')" class="menu-option">
                    <span class="menu-icon">🔧</span>
                    <span>Nodes</span>
                </button>
                <button @click="openSidebar('settings')" class="menu-option">
                    <span class="menu-icon">⚙️</span>
                    <span>Settings</span>
                </button>
                <button @click="openSidebar('files')" class="menu-option">
                    <span class="menu-icon">📁</span>
                    <span>Files</span>
                </button>
            </div>
        </div>

        <!-- File Modal -->
        <div v-if="showModal" class="modal-overlay" @click="closeModal">
            <div class="modal" @click.stop>
                <h3>{{ modalTitle }}</h3>
                <input v-model="filename" placeholder="Enter filename" />
                <div v-if="currentAction === 'save-as'" class="format-selection">
                    <label>Format:</label>
                    <select v-model="saveFormat">
                        <option value="midi">MIDI (.mid)</option>
                        <option value="weave">WEAVE (.weave)</option>
                    </select>
                </div>
                <div class="modal-buttons">
                    <button @click="confirmAction">{{ modalAction }}</button>
                    <button @click="closeModal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/components.js?v=16" defer></script>
    <script src="/static/app.js?v=16" defer></script>
</body>
</html>
