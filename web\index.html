<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Node Editor</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="https://cdn.jsdelivr.net/npm/rete@1.4.5/build/rete.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-connection-plugin@0.9.0/build/connection-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-vue-render-plugin@0.5.1/build/vue-render-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-context-menu-plugin@0.6.0/build/context-menu-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-area-plugin@0.2.1/build/area-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
</head>
<body>
    <div id="app">
        <div class="toolbar">
            <h1>MIDI Node Editor</h1>
            <div class="toolbar-buttons">
                <div class="button-group">
                    <label>MIDI Nodes:</label>
                    <button @click="addMidiInputNode">MIDI Input</button>
                    <button @click="addMidiFilterNode">Filter</button>
                    <button @click="addMidiTransformNode">Transform</button>
                    <button @click="addMidiVisualizerNode">Visualizer</button>
                    <button @click="addMidiOutputNode">MIDI Output</button>
                </div>
                <div class="button-group">
                    <label>Basic Nodes:</label>
                    <button @click="addInputNode">Input</button>
                    <button @click="addProcessNode">Process</button>
                    <button @click="addOutputNode">Output</button>
                </div>
                <div class="button-group">
                    <label>Actions:</label>
                    <button @click="clearGraph">Clear</button>
                    <button @click="saveGraph">Save</button>
                    <button @click="loadGraph">Load</button>
                </div>
            </div>
        </div>
        <div id="editor"></div>
        
        <!-- Save/Load Modal -->
        <div v-if="showModal" class="modal-overlay" @click="closeModal">
            <div class="modal" @click.stop>
                <h3>{{ modalTitle }}</h3>
                <input v-model="filename" placeholder="Enter filename" />
                <div class="modal-buttons">
                    <button @click="confirmAction">{{ modalAction }}</button>
                    <button @click="closeModal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/components.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>
