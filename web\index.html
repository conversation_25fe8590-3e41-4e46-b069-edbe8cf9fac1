<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Weaver - Professional MIDI Editor</title>
    <link rel="stylesheet" href="/static/style.css?v=8" preload>
    <!-- Core Libraries - Optimized Loading -->
    <script src="https://cdn.jsdelivr.net/npm/rete@1.4.5/build/rete.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-connection-plugin@0.9.0/build/connection-plugin.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-area-plugin@0.2.1/build/area-plugin.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js" defer></script>
</head>
<body>
    <div id="app">
        <div class="toolbar">
            <h1>Pat<PERSON> Weaver</h1>
            <div class="toolbar-buttons">

                <div class="button-group">
                    <label>File:</label>
                    <button @click="loadMidiFile">Load MIDI</button>
                    <button @click="loadWeaveFile">Load WEAVE</button>
                    <button @click="showSaveAsMenu">Save As...</button>
                    <button @click="clearGraph">Clear</button>
                </div>

                <div class="button-group">
                    <label>Transport:</label>
                    <button @click="playSequence" :disabled="isPlaying">▶ Play</button>
                    <button @click="pauseSequence" :disabled="!isPlaying">⏸ Pause</button>
                    <button @click="stopSequence">⏹ Stop</button>
                </div>



            </div>
        </div>
        <div class="main-content">
            <div class="sidebar">
                <div class="sidebar-header">Node Library</div>
                <div class="sidebar-content">
                    <div class="node-category">
                        <h4>MIDI Processing</h4>
                        <div class="node-list">
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI File Input', 'midiInput')"
                                 @click="addMidiInputNode">
                                <span class="node-icon">🎵</span>
                                <span>MIDI Input</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'Piano Roll', 'pianoRoll')"
                                 @click="addPianoRollNode">
                                <span class="node-icon">🎹</span>
                                <span>Piano Roll</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Filter', 'midiFilter')"
                                 @click="addMidiFilterNode">
                                <span class="node-icon">🔍</span>
                                <span>Filter</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Transform', 'midiTransform')"
                                 @click="addMidiTransformNode">
                                <span class="node-icon">⚡</span>
                                <span>Transform</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Visualizer', 'midiVisualizer')"
                                 @click="addMidiVisualizerNode">
                                <span class="node-icon">📊</span>
                                <span>Visualizer</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Output', 'midiOutput')"
                                 @click="addMidiOutputNode">
                                <span class="node-icon">🎹</span>
                                <span>MIDI Output</span>
                            </div>
                        </div>
                    </div>

                    <!-- Debug Section -->
                    <div class="section">
                        <h3>🔧 Debug & Tools</h3>
                        <div class="node-grid">
                            <button @click="deleteAllNodes"
                                    class="debug-btn"
                                    style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                                           color: white;
                                           border: none;
                                           padding: 8px 12px;
                                           border-radius: 6px;
                                           cursor: pointer;
                                           font-size: 12px;
                                           font-weight: 500;
                                           width: 100%;
                                           margin-bottom: 8px;">
                                🗑️ Delete All Nodes
                            </button>
                            <div style="font-size: 10px; color: #9ca3af; text-align: center; margin-top: 4px;">
                                Clears all nodes for Windows-style interface
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div id="editor"
                 @dragover.prevent
                 @drop="handleDrop($event)">
                <!-- SVG Gradients for connections -->
                <svg style="position: absolute; width: 0; height: 0;">
                    <defs>
                        <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="selectedConnectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                </svg>
            </div>
        </div>
        
        <!-- File Modal -->
        <div v-if="showModal" class="modal-overlay" @click="closeModal">
            <div class="modal" @click.stop>
                <h3>{{ modalTitle }}</h3>
                <input v-model="filename" placeholder="Enter filename" />
                <div v-if="currentAction === 'save-as'" class="format-selection">
                    <label>Format:</label>
                    <select v-model="saveFormat">
                        <option value="midi">MIDI (.mid)</option>
                        <option value="weave">WEAVE (.weave)</option>
                    </select>
                </div>
                <div class="modal-buttons">
                    <button @click="confirmAction">{{ modalAction }}</button>
                    <button @click="closeModal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/components.js?v=8" defer></script>
    <script src="/static/app.js?v=8" defer></script>
</body>
</html>
