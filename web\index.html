<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pattern Weaver</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="https://cdn.jsdelivr.net/npm/rete@1.4.5/build/rete.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-connection-plugin@0.9.0/build/connection-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-vue-render-plugin@0.5.1/build/vue-render-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-context-menu-plugin@0.6.0/build/context-menu-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-area-plugin@0.2.1/build/area-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
</head>
<body>
    <div id="app">
        <div class="toolbar">
            <h1>Pattern Weaver</h1>
            <div class="toolbar-buttons">

                <div class="button-group">
                    <label>File:</label>
                    <button @click="loadMidiFile">Load MIDI</button>
                    <button @click="loadWeaveFile">Load WEAVE</button>
                    <button @click="showSaveAsMenu">Save As...</button>
                    <button @click="clearGraph">Clear</button>
                </div>

                <div class="button-group">
                    <label>Transport:</label>
                    <button @click="playSequence" :disabled="isPlaying">▶ Play</button>
                    <button @click="pauseSequence" :disabled="!isPlaying">⏸ Pause</button>
                    <button @click="stopSequence">⏹ Stop</button>
                </div>

            </div>
        </div>
        <div class="main-content">
            <div class="sidebar">
                <div class="sidebar-header">Node Library</div>
                <div class="sidebar-content">
                    <div class="node-category">
                        <h4>MIDI Processing</h4>
                        <div class="node-list">
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI File Input', 'midiInput')"
                                 @click="addMidiInputNode">
                                <span class="node-icon">🎵</span>
                                <span>MIDI Input</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'Piano Roll', 'pianoRoll')"
                                 @click="addPianoRollNode">
                                <span class="node-icon">🎹</span>
                                <span>Piano Roll</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Filter', 'midiFilter')"
                                 @click="addMidiFilterNode">
                                <span class="node-icon">🔍</span>
                                <span>Filter</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Transform', 'midiTransform')"
                                 @click="addMidiTransformNode">
                                <span class="node-icon">⚡</span>
                                <span>Transform</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Visualizer', 'midiVisualizer')"
                                 @click="addMidiVisualizerNode">
                                <span class="node-icon">📊</span>
                                <span>Visualizer</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'MIDI Output', 'midiOutput')"
                                 @click="addMidiOutputNode">
                                <span class="node-icon">🎹</span>
                                <span>MIDI Output</span>
                            </div>
                        </div>
                    </div>
                    <div class="node-category">
                        <h4>Basic Nodes</h4>
                        <div class="node-list">
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'Input', 'input')"
                                 @click="addInputNode">
                                <span class="node-icon">📥</span>
                                <span>Input</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'Process', 'process')"
                                 @click="addProcessNode">
                                <span class="node-icon">⚙️</span>
                                <span>Process</span>
                            </div>
                            <div class="node-item"
                                 draggable="true"
                                 @dragstart="startDrag($event, 'Output', 'output')"
                                 @click="addOutputNode">
                                <span class="node-icon">📤</span>
                                <span>Output</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="editor"
                 @dragover.prevent
                 @drop="handleDrop($event)">
                <!-- SVG Gradients for connections -->
                <svg style="position: absolute; width: 0; height: 0;">
                    <defs>
                        <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="selectedConnectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                </svg>
            </div>
        </div>
        
        <!-- File Modal -->
        <div v-if="showModal" class="modal-overlay" @click="closeModal">
            <div class="modal" @click.stop>
                <h3>{{ modalTitle }}</h3>
                <input v-model="filename" placeholder="Enter filename" />
                <div v-if="currentAction === 'save-as'" class="format-selection">
                    <label>Format:</label>
                    <select v-model="saveFormat">
                        <option value="midi">MIDI (.mid)</option>
                        <option value="weave">WEAVE (.weave)</option>
                    </select>
                </div>
                <div class="modal-buttons">
                    <button @click="confirmAction">{{ modalAction }}</button>
                    <button @click="closeModal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/components.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>
