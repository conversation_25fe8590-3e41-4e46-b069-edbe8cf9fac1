@echo off
echo Testing Web Node Editor Build...

REM Check if build directory exists
if not exist build (
    echo Build directory not found. Running build first...
    call build.bat
    if %errorlevel% neq 0 (
        echo Build failed!
        pause
        exit /b 1
    )
)

REM Check if executable exists
if not exist build\Release\web_node_editor.exe (
    if not exist build\Debug\web_node_editor.exe (
        echo Executable not found! Build may have failed.
        pause
        exit /b 1
    )
    set EXECUTABLE=build\Debug\web_node_editor.exe
) else (
    set EXECUTABLE=build\Release\web_node_editor.exe
)

echo Found executable: %EXECUTABLE%
echo.
echo Starting server...
echo Open your browser to http://localhost:8080
echo Press Ctrl+C to stop the server
echo.

%EXECUTABLE%
