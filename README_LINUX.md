# Pattern Weaver - Linux Compatibility Guide

Pat<PERSON> Weaver is now fully cross-platform compatible! This guide covers Linux-specific installation and usage.

## 🐧 Linux Compatibility Features

### ✅ **Removed Windows Dependencies:**
- **No more Windows.h**: Completely eliminated Windows-specific headers
- **Cross-platform sockets**: Uses POSIX sockets on Linux, WinSock on Windows
- **ALSA MIDI support**: Native Linux MIDI through ALSA instead of WinMM
- **POSIX file operations**: Linux-compatible file and directory operations
- **Standard threading**: Uses std::thread instead of Windows threading APIs

### 🎵 **MIDI Support on Linux:**
- **ALSA integration**: Native ALSA rawmidi support for low-latency MIDI
- **Multiple device support**: Automatically detects available MIDI devices
- **Fallback devices**: Tries multiple device names (default, hw:0,0, virtual, hw:1,0)
- **Error handling**: Graceful fallback when MIDI devices are unavailable

## 📦 Installation

### Quick Installation (Recommended)

```bash
# 1. Install dependencies
chmod +x install_linux_deps.sh
./install_linux_deps.sh

# 2. Build Pattern Weaver
chmod +x build_linux.sh
./build_linux.sh

# 3. Run the server
cd build
./midi_node_editor
```

### Manual Installation

#### Dependencies
Install the required packages for your distribution:

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install build-essential cmake libasound2-dev pkg-config git
```

**CentOS/RHEL/Fedora:**
```bash
# Fedora (dnf)
sudo dnf groupinstall "Development Tools"
sudo dnf install cmake alsa-lib-devel pkgconfig git

# CentOS/RHEL (yum)
sudo yum groupinstall "Development Tools"
sudo yum install cmake alsa-lib-devel pkgconfig git
```

**Arch Linux:**
```bash
sudo pacman -Sy base-devel cmake alsa-lib pkgconf git
```

**openSUSE:**
```bash
sudo zypper install gcc-c++ cmake alsa-devel pkg-config git
```

#### Build Process
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
cp -r ../web ../saves .
```

## 🎛️ MIDI Configuration on Linux

### Setting up ALSA MIDI

1. **Check ALSA installation:**
   ```bash
   aplay -l    # List audio devices
   amidi -l    # List MIDI devices
   ```

2. **Install ALSA utilities (if needed):**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install alsa-utils
   
   # Fedora
   sudo dnf install alsa-utils
   ```

3. **Create virtual MIDI ports (optional):**
   ```bash
   # Load the snd-virmidi module for virtual MIDI ports
   sudo modprobe snd-virmidi midi_devs=2
   
   # Make it permanent
   echo "snd-virmidi midi_devs=2" | sudo tee -a /etc/modules
   ```

### Connecting to Software Synthesizers

Pattern Weaver can connect to various Linux software synthesizers:

- **FluidSynth**: `sudo apt-get install fluidsynth`
- **TiMidity++**: `sudo apt-get install timidity`
- **QSynth**: GUI for FluidSynth
- **ZynAddSubFX**: Advanced software synthesizer

## 🚀 Running Pattern Weaver

```bash
cd build
./midi_node_editor
```

Then open your browser to: **http://localhost:8081**

## 🔧 Troubleshooting

### MIDI Issues
- **No MIDI output**: Check `amidi -l` for available devices
- **Permission denied**: Add user to audio group: `sudo usermod -a -G audio $USER`
- **No sound**: Install and configure a software synthesizer

### Build Issues
- **ALSA not found**: Install `libasound2-dev` (Ubuntu) or `alsa-lib-devel` (CentOS)
- **CMake errors**: Ensure CMake 3.15+ is installed
- **Compiler errors**: Install `build-essential` or equivalent

### Network Issues
- **Port 8081 in use**: Change port in source code or kill conflicting process
- **Firewall blocking**: Open port 8081 or disable firewall temporarily

## 🎨 Features Available on Linux

All Pattern Weaver features work identically on Linux:

- ✅ **Windows-style nodes** with minimize/maximize/close
- ✅ **Global BPM/PPQ settings**
- ✅ **Floating menu system**
- ✅ **Color palette customization**
- ✅ **MIDI file loading and processing**
- ✅ **High-performance multithreading**
- ✅ **Professional dark theme**

## 📝 Development Notes

### Cross-Platform Code Structure
```cpp
#ifdef _WIN32
    // Windows-specific code (WinSock, WinMM)
#else
    // Linux-specific code (POSIX sockets, ALSA)
#endif
```

### Key Changes Made
1. **Socket abstraction**: `socket_t` type for cross-platform sockets
2. **MIDI abstraction**: `CrossPlatformMidiOutput` class
3. **File operations**: POSIX `mkdir()` instead of `CreateDirectoryA()`
4. **Error handling**: Platform-specific error codes
5. **Build system**: CMake with platform detection

## 🤝 Contributing

When contributing cross-platform code:
1. Test on both Windows and Linux
2. Use `#ifdef _WIN32` for platform-specific code
3. Prefer standard C++ over platform-specific APIs
4. Update both build scripts when adding dependencies

---

**Pattern Weaver** - Professional MIDI Editor with Cross-Platform Compatibility
