<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MIDI Node Editor - Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #2d2d2d;
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #4CAF50;
        }
        
        .header h1 {
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .status {
            color: #cccccc;
            font-size: 14px;
        }
        
        .main {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .test-section {
            background: #2d2d2d;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
            min-width: 400px;
            text-align: center;
            border: 1px solid #404040;
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background-color 0.3s;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #1e1e1e;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
            text-align: left;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success {
            color: #4CAF50;
        }
        
        .error {
            color: #f44336;
            border-left-color: #f44336;
        }
        
        .info {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        
        .info h3 {
            color: #2196F3;
            margin-bottom: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: #cccccc;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎵 MIDI Node Editor</h1>
        <div class="status">High-Performance MIDI Processing System</div>
    </div>
    
    <div class="main">
        <div class="test-section">
            <h2>System Test</h2>
            <p>Click the button below to test the backend connection:</p>
            <button class="test-button" onclick="testBackend()">Test Backend Connection</button>
            <div id="test-result"></div>
        </div>
        
        <div class="info">
            <h3>🚀 Next Steps</h3>
            <p>Once the backend test passes, you can:</p>
            <ul class="feature-list">
                <li>Install full node editor dependencies</li>
                <li>Load and process MIDI files</li>
                <li>Use the visual node interface</li>
                <li>Apply filters and transformations</li>
                <li>Export processed MIDI files</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>🎹 Planned Features</h3>
            <ul class="feature-list">
                <li>Handle 10+ million MIDI notes</li>
                <li>Real-time piano roll visualization</li>
                <li>Advanced filtering and transformations</li>
                <li>Proprietary fast-loading format</li>
                <li>Memory-efficient processing</li>
                <li>Multi-threaded parsing</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>File Upload Test</h2>
            <p>Test MIDI file upload (when backend is ready):</p>
            <input type="file" id="midiFile" accept=".mid,.midi" style="margin: 10px; padding: 5px;">
            <button class="test-button" onclick="testFileUpload()">Upload MIDI File</button>
            <div id="upload-result"></div>
        </div>
    </div>

    <script>
        async function testBackend() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="result">Testing backend connection...</div>';
            
            try {
                const response = await fetch('/api/test');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ Backend Connection Successful!
                            
                            Status: ${data.status}
                            Message: ${data.message}
                            
                            Server is running and ready for MIDI processing.
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Backend Connection Failed
                        
                        Error: ${error.message}
                        
                        Make sure the server is running on port 8080.
                    </div>
                `;
            }
        }
        
        async function testFileUpload() {
            const fileInput = document.getElementById('midiFile');
            const resultDiv = document.getElementById('upload-result');
            
            if (!fileInput.files.length) {
                resultDiv.innerHTML = '<div class="result error">Please select a MIDI file first.</div>';
                return;
            }
            
            const file = fileInput.files[0];
            resultDiv.innerHTML = `<div class="result">Uploading ${file.name}...</div>`;
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                
                const response = await fetch('/api/upload-midi', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ File Upload Successful!
                            
                            File: ${file.name}
                            Size: ${(file.size / 1024).toFixed(1)} KB
                            
                            Ready for processing in the node editor.
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ File Upload Failed
                        
                        Error: ${error.message}
                        
                        This feature requires the full MIDI processing backend.
                    </div>
                `;
            }
        }
        
        // Test backend connection on page load
        window.addEventListener('load', () => {
            setTimeout(testBackend, 1000);
        });
    </script>
</body>
</html>
