#pragma once

#include <vector>
#include <string>
#include <fstream>
#include <cstdint>
#include <memory>

struct MidiNote {
    uint32_t start_tick;
    uint32_t end_tick;
    uint8_t channel;
    uint8_t note;
    uint8_t velocity;
    uint16_t track_id;
    
    bool operator<(const MidiNote& other) const {
        return start_tick < other.start_tick;
    }
};

struct MidiEvent {
    uint32_t tick;
    uint8_t type;
    uint8_t channel;
    std::vector<uint8_t> data;
    uint16_t track_id;
};

struct MidiHeader {
    uint16_t format;
    uint16_t track_count;
    uint16_t ticks_per_quarter;
};

class FastMidiParser {
private:
    std::vector<uint8_t> file_data;
    size_t position = 0;
    
    uint32_t read_variable_length();
    uint32_t read_uint32();
    uint16_t read_uint16();
    uint8_t read_uint8();
    void skip_bytes(size_t count);
    
public:
    struct ParseResult {
        MidiHeader header;
        std::vector<MidiNote> notes;
        std::vector<MidiEvent> events;
        bool success = false;
        std::string error_message;
        double parse_time_ms = 0.0;
    };
    
    ParseResult parse_file(const std::string& filename);
    ParseResult parse_data(const std::vector<uint8_t>& data);
    
    // High-performance parsing for massive files
    ParseResult parse_file_streaming(const std::string& filename, 
                                   std::function<void(const std::vector<MidiNote>&)> note_callback = nullptr);
    
    // Export to proprietary text format for faster loading
    bool export_to_fast_format(const std::string& input_midi, const std::string& output_file);
    ParseResult load_from_fast_format(const std::string& filename);
};

// Proprietary text format for ultra-fast loading
class FastMidiFormat {
public:
    struct Header {
        uint32_t version = 1;
        uint32_t note_count;
        uint32_t event_count;
        uint16_t ticks_per_quarter;
        uint32_t total_ticks;
    };
    
    // Binary format optimized for memory mapping
    static bool save(const std::string& filename, 
                    const MidiHeader& header,
                    const std::vector<MidiNote>& notes,
                    const std::vector<MidiEvent>& events);
    
    static bool load(const std::string& filename,
                    MidiHeader& header,
                    std::vector<MidiNote>& notes,
                    std::vector<MidiEvent>& events);
    
    // Memory-mapped loading for instant access to huge files
    static std::unique_ptr<class MidiMemoryMap> memory_map(const std::string& filename);
};

// Memory-mapped MIDI file for zero-copy access
class MidiMemoryMap {
private:
    void* mapped_data = nullptr;
    size_t file_size = 0;
    
public:
    ~MidiMemoryMap();
    
    bool open(const std::string& filename);
    void close();
    
    const MidiNote* get_notes() const;
    size_t get_note_count() const;
    
    const MidiEvent* get_events() const;
    size_t get_event_count() const;
    
    const MidiHeader& get_header() const;
    
    // Fast range queries without copying data
    class NoteIterator {
    private:
        const MidiNote* current;
        const MidiNote* end;
        uint32_t start_tick;
        uint32_t end_tick;
        
    public:
        NoteIterator(const MidiNote* start, const MidiNote* end, uint32_t start_t, uint32_t end_t);
        
        const MidiNote& operator*() const { return *current; }
        const MidiNote* operator->() const { return current; }
        
        NoteIterator& operator++();
        bool operator!=(const NoteIterator& other) const { return current != other.current; }
    };
    
    NoteIterator notes_in_range(uint32_t start_tick, uint32_t end_tick) const;
};

// MIDI processing utilities
namespace MidiUtils {
    // Convert MIDI ticks to time
    double ticks_to_seconds(uint32_t ticks, uint16_t ticks_per_quarter, uint32_t tempo_bpm = 120);
    uint32_t seconds_to_ticks(double seconds, uint16_t ticks_per_quarter, uint32_t tempo_bpm = 120);
    
    // Note name conversion
    std::string note_to_name(uint8_t note);
    uint8_t name_to_note(const std::string& name);
    
    // Statistics
    struct MidiStats {
        uint32_t total_notes;
        uint32_t total_duration_ticks;
        double total_duration_seconds;
        uint8_t min_note;
        uint8_t max_note;
        uint8_t note_range;
        std::vector<uint32_t> notes_per_channel;
        std::vector<uint32_t> notes_per_track;
    };
    
    MidiStats analyze(const std::vector<MidiNote>& notes, uint16_t ticks_per_quarter);
    
    // High-performance operations
    void transpose_notes(std::vector<MidiNote>& notes, int8_t semitones);
    void scale_velocity(std::vector<MidiNote>& notes, double factor);
    void time_stretch(std::vector<MidiNote>& notes, double factor);
    
    // Filtering
    std::vector<MidiNote> filter_by_channel(const std::vector<MidiNote>& notes, uint8_t channel);
    std::vector<MidiNote> filter_by_note_range(const std::vector<MidiNote>& notes, uint8_t min_note, uint8_t max_note);
    std::vector<MidiNote> filter_by_time_range(const std::vector<MidiNote>& notes, uint32_t start_tick, uint32_t end_tick);
}
