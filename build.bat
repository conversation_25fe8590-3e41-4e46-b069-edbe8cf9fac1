@echo off
echo Building Web Node Editor...

REM Create build directory
if not exist build mkdir build
cd build

REM Try different generators in order of preference
echo Trying Visual Studio 17 2022...
cmake .. -G "Visual Studio 17 2022" -A x64 2>nul
if %errorlevel% equ 0 goto build

echo Trying Visual Studio 16 2019...
cmake .. -G "Visual Studio 16 2019" -A x64 2>nul
if %errorlevel% equ 0 goto build

echo Trying Visual Studio 15 2017...
cmake .. -G "Visual Studio 15 2017" -A x64 2>nul
if %errorlevel% equ 0 goto build

echo Trying MinGW Makefiles...
cmake .. -G "MinGW Makefiles" 2>nul
if %errorlevel% equ 0 goto build

echo Trying Ninja...
cmake .. -G "Ninja" 2>nul
if %errorlevel% equ 0 goto build

echo Trying default generator...
cmake .. 2>nul
if %errorlevel% equ 0 goto build

echo All CMake generators failed!
echo.
echo Please install one of the following:
echo - Visual Studio 2017, 2019, or 2022 with C++ support
echo - MinGW-w64
echo - Ninja build system
echo.
pause
exit /b 1

:build

REM Build the project
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo.
echo To run the application:
echo   cd build\Release
echo   web_node_editor.exe
echo.
echo Then open your browser to http://localhost:8080
pause
