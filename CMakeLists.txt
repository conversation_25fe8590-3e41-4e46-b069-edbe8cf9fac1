cmake_minimum_required(VERSION 3.15)
project(WebNodeEditor)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Create directories
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/web)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/web/static)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/saves)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/uploads)

# Add executable
add_executable(midi_node_editor simple_main.cpp)

# Platform-specific linking
if(WIN32)
    target_link_libraries(midi_node_editor PRIVATE ws2_32 wsock32)
else()
    # Linux dependencies
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(ALSA REQUIRED alsa)
    target_link_libraries(midi_node_editor PRIVATE ${ALSA_LIBRARIES} pthread)
    target_include_directories(midi_node_editor PRIVATE ${ALSA_INCLUDE_DIRS})
    target_compile_options(midi_node_editor PRIVATE ${ALSA_CFLAGS_OTHER})
endif()

# Compiler-specific options
if(MSVC)
    target_compile_options(midi_node_editor PRIVATE /W4)
else()
    target_compile_options(midi_node_editor PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Copy web files to build directory
add_custom_command(TARGET midi_node_editor POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_CURRENT_SOURCE_DIR}/web
    $<TARGET_FILE_DIR:midi_node_editor>/web
)

# Copy saves directory to build directory
add_custom_command(TARGET midi_node_editor POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_CURRENT_SOURCE_DIR}/saves
    $<TARGET_FILE_DIR:midi_node_editor>/saves
)

# Copy uploads directory to build directory
add_custom_command(TARGET midi_node_editor POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_CURRENT_SOURCE_DIR}/uploads
    $<TARGET_FILE_DIR:midi_node_editor>/uploads
)
