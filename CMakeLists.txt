cmake_minimum_required(VERSION 3.15)
project(WebNodeEditor)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find packages
find_package(Threads REQUIRED)

# Create directories for external dependencies
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/external)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/web)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/web/static)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/saves)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/uploads)

# Download and setup Crow if not exists
if(NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/external/crow)
    message(STATUS "Downloading Crow...")
    file(DOWNLOAD 
        "https://github.com/CrowCpp/Crow/releases/download/v1.0%2B5/crow-v1.0+5.tar.gz"
        "${CMAKE_CURRENT_SOURCE_DIR}/external/crow.tar.gz"
        SHOW_PROGRESS
    )
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E tar xzf crow.tar.gz
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/external
    )
    file(RENAME ${CMAKE_CURRENT_SOURCE_DIR}/external/crow-v1.0+5 ${CMAKE_CURRENT_SOURCE_DIR}/external/crow)
endif()

# Download nlohmann/json if not exists
if(NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/external/json)
    message(STATUS "Downloading nlohmann/json...")
    file(DOWNLOAD 
        "https://github.com/nlohmann/json/releases/download/v3.11.2/json.tar.xz"
        "${CMAKE_CURRENT_SOURCE_DIR}/external/json.tar.xz"
        SHOW_PROGRESS
    )
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E tar xf json.tar.xz
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/external
    )
    file(RENAME ${CMAKE_CURRENT_SOURCE_DIR}/external/json ${CMAKE_CURRENT_SOURCE_DIR}/external/json)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/external/crow/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/external/json/include)

# Add executable
add_executable(web_node_editor main.cpp midi_parser.cpp)

# Link libraries
target_link_libraries(web_node_editor PRIVATE Threads::Threads)

# Platform-specific linking
if(WIN32)
    target_link_libraries(web_node_editor PRIVATE ws2_32 wsock32)
elseif(UNIX AND NOT APPLE)
    target_link_libraries(web_node_editor PRIVATE pthread)
endif()

# Compiler-specific options
if(MSVC)
    target_compile_options(web_node_editor PRIVATE /W4)
else()
    target_compile_options(web_node_editor PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Copy web files to build directory
add_custom_command(TARGET web_node_editor POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_CURRENT_SOURCE_DIR}/web
    $<TARGET_FILE_DIR:web_node_editor>/web
)

# Copy saves directory to build directory
add_custom_command(TARGET web_node_editor POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_CURRENT_SOURCE_DIR}/saves
    $<TARGET_FILE_DIR:web_node_editor>/saves
)

# Copy uploads directory to build directory
add_custom_command(TARGET web_node_editor POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_CURRENT_SOURCE_DIR}/uploads
    $<TARGET_FILE_DIR:web_node_editor>/uploads
)
