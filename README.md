# Web Node Editor

A web-based visual node editor with a C++ backend using Crow web framework and a modern JavaScript frontend using Rete.js.

## Features

- **Visual Node Editor**: Drag-and-drop interface for creating node graphs
- **Real-time Processing**: Nodes process data in real-time as connections change
- **Save/Load**: Persist node graphs to files
- **RESTful API**: Clean API for node and connection management
- **Modern UI**: Dark theme with responsive design
- **Cross-platform**: Works on Windows, Linux, and macOS

## Node Types

- **Input Nodes**: Generate or input data values
- **Process Nodes**: Perform mathematical operations (add, subtract, multiply, divide)
- **Output Nodes**: Display results

## Prerequisites

- CMake 3.15 or higher
- C++20 compatible compiler (GCC 10+, Clang 10+, MSVC 2019+)
- Internet connection (for downloading dependencies)

## Building

### Windows
```bash
# Run the build script
build.bat

# Or manually:
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### Linux/macOS
```bash
# Make the script executable and run it
chmod +x build.sh
./build.sh

# Or manually:
mkdir build
cd build
cmake ..
make -j$(nproc)
```

## Running

1. Navigate to the build directory:
   ```bash
   cd build/Release  # Windows
   cd build          # Linux/macOS
   ```

2. Run the executable:
   ```bash
   web_node_editor.exe  # Windows
   ./web_node_editor    # Linux/macOS
   ```

3. Open your web browser and go to: `http://localhost:8080`

## Usage

### Creating Nodes
- Use the toolbar buttons to add Input, Process, or Output nodes
- Right-click in the editor area for a context menu with more options

### Connecting Nodes
- Click and drag from an output socket (right side) to an input socket (left side)
- Connections will automatically process data flow

### Node Configuration
- Input nodes: Set values using the text input
- Process nodes: Choose operation type (add, subtract, multiply, divide)
- Output nodes: Display computed results

### Saving and Loading
- Click "Save" to save your current graph to a file
- Click "Load" to load a previously saved graph
- Files are stored in the `saves/` directory

## Project Structure

```
├── main.cpp              # C++ backend server
├── CMakeLists.txt        # Build configuration
├── web/
│   ├── index.html        # Main HTML page
│   └── static/
│       ├── style.css     # Styling
│       ├── components.js # Node component definitions
│       └── app.js        # Main application logic
├── saves/                # Saved graph files
├── build.bat            # Windows build script
├── build.sh             # Linux/macOS build script
└── README.md            # This file
```

## API Endpoints

- `GET /api/graph` - Get current graph data
- `POST /api/nodes` - Create a new node
- `PUT /api/nodes/{id}` - Update a node
- `DELETE /api/nodes/{id}` - Delete a node
- `POST /api/connections` - Create a new connection
- `DELETE /api/connections/{id}` - Delete a connection
- `POST /api/save` - Save graph to file
- `POST /api/load` - Load graph from file

## Dependencies

The build system automatically downloads:
- **Crow**: Lightweight C++ web framework
- **nlohmann/json**: Modern C++ JSON library

Frontend uses CDN-hosted libraries:
- **Rete.js**: Visual programming framework
- **Vue.js**: Reactive UI framework

## Extending

To add new node types:

1. Create a new component class in `web/static/components.js`
2. Register it in the editor initialization in `web/static/app.js`
3. Add corresponding processing logic in the C++ backend if needed

## License

This project is open source. Feel free to modify and distribute.

## Troubleshooting

### Build Issues
- Ensure you have a C++20 compatible compiler
- Check that CMake can access the internet to download dependencies
- On Windows, make sure Visual Studio 2019 or later is installed

### Runtime Issues
- Check that port 8080 is not in use by another application
- Ensure the `web/` and `saves/` directories exist in the same location as the executable
- Check the console output for any error messages

### Browser Issues
- Try refreshing the page if nodes don't appear
- Check the browser console for JavaScript errors
- Ensure you're using a modern browser (Chrome, Firefox, Safari, Edge)
