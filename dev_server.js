// Simple Node.js development server for testing the MIDI Node Editor frontend
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 8080;

// MIME types for different file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
};

// Mock MIDI data for testing
const mockMidiData = {
    nodes: [
        {
            id: 1,
            type: "midi_input",
            name: "Sample MIDI",
            x: 100,
            y: 100,
            data: { filename: "sample.mid", note_count: 1500 }
        },
        {
            id: 2,
            type: "midi_filter",
            name: "Channel Filter",
            x: 350,
            y: 100,
            data: { filter_type: "channel", min_value: 1, max_value: 8 }
        },
        {
            id: 3,
            type: "midi_visualizer",
            name: "Piano Roll",
            x: 600,
            y: 100,
            data: { view_type: "piano_roll" }
        }
    ],
    connections: [
        { id: 1, sourceNodeId: 1, sourcePortId: 0, targetNodeId: 2, targetPortId: 0 },
        { id: 2, sourceNodeId: 2, sourcePortId: 0, targetNodeId: 3, targetPortId: 0 }
    ]
};

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // API endpoints
    if (pathname.startsWith('/api/')) {
        handleApiRequest(req, res, pathname, parsedUrl.query);
        return;
    }
    
    // Serve static files
    let filePath = pathname === '/' ? '/web/index.html' : pathname;
    
    // If requesting simple test page
    if (pathname === '/simple' || pathname === '/test') {
        filePath = '/web/simple.html';
    }
    
    filePath = path.join(__dirname, filePath);
    
    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
        }
        
        // Get file extension and MIME type
        const ext = path.extname(filePath);
        const mimeType = mimeTypes[ext] || 'text/plain';
        
        // Read and serve file
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('Internal server error');
                return;
            }
            
            res.writeHead(200, { 'Content-Type': mimeType });
            res.end(data);
        });
    });
});

function handleApiRequest(req, res, pathname, query) {
    res.setHeader('Content-Type', 'application/json');
    
    switch (pathname) {
        case '/api/test':
            res.writeHead(200);
            res.end(JSON.stringify({
                status: 'ok',
                message: 'MIDI Node Editor Development Server is running!',
                note: 'This is a mock server for frontend development'
            }));
            break;
            
        case '/api/graph':
            res.writeHead(200);
            res.end(JSON.stringify(mockMidiData));
            break;
            
        case '/api/midi-stats':
            res.writeHead(200);
            res.end(JSON.stringify({
                total_notes: 1500,
                memory_usage_mb: 0.1,
                parse_time_ms: 45,
                status: 'mock_data'
            }));
            break;
            
        case '/api/midi-range':
            const startTick = parseInt(query.start) || 0;
            const endTick = parseInt(query.end) || 1000;
            const maxNotes = parseInt(query.max) || 100;
            
            // Generate mock notes for the range
            const mockNotes = [];
            for (let i = 0; i < Math.min(maxNotes, 50); i++) {
                mockNotes.push({
                    start: startTick + (i * 10),
                    end: startTick + (i * 10) + 5,
                    channel: Math.floor(Math.random() * 16),
                    note: 60 + Math.floor(Math.random() * 24),
                    velocity: 64 + Math.floor(Math.random() * 63),
                    track: 0
                });
            }
            
            res.writeHead(200);
            res.end(JSON.stringify({
                notes: mockNotes,
                total_notes: 1500,
                displayed_notes: mockNotes.length,
                start_tick: startTick,
                end_tick: endTick
            }));
            break;
            
        case '/api/upload-midi':
            if (req.method === 'POST') {
                // Mock file upload response
                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    note_count: 1500,
                    parse_time_ms: 45,
                    filename: 'uploaded.mid',
                    message: 'Mock upload successful (development server)'
                }));
            } else {
                res.writeHead(405);
                res.end(JSON.stringify({ error: 'Method not allowed' }));
            }
            break;
            
        case '/api/export-midi':
            if (req.method === 'POST') {
                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    filename: 'export.mid',
                    format: 'midi',
                    message: 'Mock export successful (development server)'
                }));
            } else {
                res.writeHead(405);
                res.end(JSON.stringify({ error: 'Method not allowed' }));
            }
            break;
            
        default:
            res.writeHead(404);
            res.end(JSON.stringify({ error: 'API endpoint not found' }));
    }
}

server.listen(PORT, () => {
    console.log('🎵 MIDI Node Editor Development Server');
    console.log('=====================================');
    console.log(`Server running at http://localhost:${PORT}`);
    console.log('');
    console.log('Available endpoints:');
    console.log('  http://localhost:8080/          - Main application');
    console.log('  http://localhost:8080/simple    - Simple test page');
    console.log('  http://localhost:8080/api/test  - API test');
    console.log('');
    console.log('This is a development server with mock data.');
    console.log('The real C++ backend will provide actual MIDI processing.');
    console.log('');
    console.log('Press Ctrl+C to stop the server');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down development server...');
    server.close(() => {
        console.log('Server stopped.');
        process.exit(0);
    });
});
