#!/bin/bash

echo "Building Web Node Editor..."

# Create build directory
mkdir -p build
cd build

# Run CMake
cmake ..
if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

# Build the project
make -j$(nproc)
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build completed successfully!"
echo ""
echo "To run the application:"
echo "  cd build"
echo "  ./web_node_editor"
echo ""
echo "Then open your browser to http://localhost:8080"
