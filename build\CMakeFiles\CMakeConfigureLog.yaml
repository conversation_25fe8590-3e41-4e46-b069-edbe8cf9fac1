
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Versi├│n de MSBuild 17.14.10+8b8e13593 para .NET Framework
      Compilaci├│n iniciada a las 21/06/2025 2:16:14ΓÇ»p.ΓÇ»m..
      
      Proyecto "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\4.0.3\\CompilerIdC\\CompilerIdC.vcxproj" en nodo 1 (destinos predeterminados).
      PrepareForBuild:
        Creando directorio "Debug\\".
        La salida estructurada est├í habilitada. El formato del diagn├│stico del compilador reflejar├í la jerarqu├¡a de errores. Consulte https://aka.ms/cpp/structured-output para obtener m├ís detalles.
        Creando directorio "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Se crear├í "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" porque se especific├│ "AlwaysCreate".
        Aplicando tarea Touch a "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\4.0.3\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Se eliminar├í el archivo "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Aplicando tarea Touch a "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Compilaci├│n del proyecto terminada "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\4.0.3\\CompilerIdC\\CompilerIdC.vcxproj" (destinos predeterminados).
      
      Compilaci├│n correcta.
          0 Advertencia(s)
          0 Errores
      
      Tiempo transcurrido 00:00:02.39
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        F:/Pattern Weaver (PW)/build/CMakeFiles/4.0.3/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Versi├│n de MSBuild 17.14.10+8b8e13593 para .NET Framework
      Compilaci├│n iniciada a las 21/06/2025 2:16:17ΓÇ»p.ΓÇ»m..
      
      Proyecto "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj" en nodo 1 (destinos predeterminados).
      PrepareForBuild:
        Creando directorio "Debug\\".
        La salida estructurada est├í habilitada. El formato del diagn├│stico del compilador reflejar├í la jerarqu├¡a de errores. Consulte https://aka.ms/cpp/structured-output para obtener m├ís detalles.
        Creando directorio "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Se crear├í "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" porque se especific├│ "AlwaysCreate".
        Aplicando tarea Touch a "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Se eliminar├í el archivo "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Aplicando tarea Touch a "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Compilaci├│n del proyecto terminada "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (destinos predeterminados).
      
      Compilaci├│n correcta.
          0 Advertencia(s)
          0 Errores
      
      Tiempo transcurrido 00:00:01.63
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        F:/Pattern Weaver (PW)/build/CMakeFiles/4.0.3/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-htnad6"
      binary: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-htnad6"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-htnad6'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ca4b0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versi├│n de MSBuild 17.14.10+8b8e13593 para .NET Framework
        Compilaci├│n iniciada a las 21/06/2025 2:16:20ΓÇ»p.ΓÇ»m..
        
        Proyecto "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-htnad6\\cmTC_ca4b0.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_ca4b0.dir\\Debug\\".
          La salida estructurada est├í habilitada. El formato del diagn├│stico del compilador reflejar├í la jerarqu├¡a de errores. Consulte https://aka.ms/cpp/structured-output para obtener m├ís detalles.
          Creando directorio "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-htnad6\\Debug\\".
          Creando directorio "cmTC_ca4b0.dir\\Debug\\cmTC_ca4b0.tlog\\".
        InitializeBuildStatus:
          Se crear├í "cmTC_ca4b0.dir\\Debug\\cmTC_ca4b0.tlog\\unsuccessfulbuild" porque se especific├│ "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_ca4b0.dir\\Debug\\cmTC_ca4b0.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ca4b0.dir\\Debug\\\\" /Fd"cmTC_ca4b0.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          Compilador de optimizaci├│n de C/C++ de Microsoft (R) versi├│n 19.44.35211 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ca4b0.dir\\Debug\\\\" /Fd"cmTC_ca4b0.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-htnad6\\Debug\\cmTC_ca4b0.exe" /INCREMENTAL /ILK:"cmTC_ca4b0.dir\\Debug\\cmTC_ca4b0.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-htnad6/Debug/cmTC_ca4b0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-htnad6/Debug/cmTC_ca4b0.lib" /MACHINE:X64  /machine:x64 cmTC_ca4b0.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_ca4b0.vcxproj -> F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-htnad6\\Debug\\cmTC_ca4b0.exe
        FinalizeBuildStatus:
          Se eliminar├í el archivo "cmTC_ca4b0.dir\\Debug\\cmTC_ca4b0.tlog\\unsuccessfulbuild".
          Aplicando tarea Touch a "cmTC_ca4b0.dir\\Debug\\cmTC_ca4b0.tlog\\cmTC_ca4b0.lastbuildstate".
        Compilaci├│n del proyecto terminada "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-htnad6\\cmTC_ca4b0.vcxproj" (destinos predeterminados).
        
        Compilaci├│n correcta.
            0 Advertencia(s)
            0 Errores
        
        Tiempo transcurrido 00:00:01.54
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-j04rxf"
      binary: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-j04rxf"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-j04rxf'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_36c15.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versi├│n de MSBuild 17.14.10+8b8e13593 para .NET Framework
        Compilaci├│n iniciada a las 21/06/2025 2:16:23ΓÇ»p.ΓÇ»m..
        
        Proyecto "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j04rxf\\cmTC_36c15.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_36c15.dir\\Debug\\".
          La salida estructurada est├í habilitada. El formato del diagn├│stico del compilador reflejar├í la jerarqu├¡a de errores. Consulte https://aka.ms/cpp/structured-output para obtener m├ís detalles.
          Creando directorio "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j04rxf\\Debug\\".
          Creando directorio "cmTC_36c15.dir\\Debug\\cmTC_36c15.tlog\\".
        InitializeBuildStatus:
          Se crear├í "cmTC_36c15.dir\\Debug\\cmTC_36c15.tlog\\unsuccessfulbuild" porque se especific├│ "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_36c15.dir\\Debug\\cmTC_36c15.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_36c15.dir\\Debug\\\\" /Fd"cmTC_36c15.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Compilador de optimizaci├│n de C/C++ de Microsoft (R) versi├│n 19.44.35211 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_36c15.dir\\Debug\\\\" /Fd"cmTC_36c15.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j04rxf\\Debug\\cmTC_36c15.exe" /INCREMENTAL /ILK:"cmTC_36c15.dir\\Debug\\cmTC_36c15.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-j04rxf/Debug/cmTC_36c15.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-j04rxf/Debug/cmTC_36c15.lib" /MACHINE:X64  /machine:x64 cmTC_36c15.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_36c15.vcxproj -> F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j04rxf\\Debug\\cmTC_36c15.exe
        FinalizeBuildStatus:
          Se eliminar├í el archivo "cmTC_36c15.dir\\Debug\\cmTC_36c15.tlog\\unsuccessfulbuild".
          Aplicando tarea Touch a "cmTC_36c15.dir\\Debug\\cmTC_36c15.tlog\\cmTC_36c15.lastbuildstate".
        Compilaci├│n del proyecto terminada "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j04rxf\\cmTC_36c15.vcxproj" (destinos predeterminados).
        
        Compilaci├│n correcta.
            0 Advertencia(s)
            0 Errores
        
        Tiempo transcurrido 00:00:01.56
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-w3bdf6"
      binary: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-w3bdf6"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-w3bdf6'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d867d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versi├│n de MSBuild 17.14.10+8b8e13593 para .NET Framework
        Compilaci├│n iniciada a las 21/06/2025 2:16:25ΓÇ»p.ΓÇ»m..
        
        Proyecto "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\cmTC_d867d.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_d867d.dir\\Debug\\".
          La salida estructurada est├í habilitada. El formato del diagn├│stico del compilador reflejar├í la jerarqu├¡a de errores. Consulte https://aka.ms/cpp/structured-output para obtener m├ís detalles.
          Creando directorio "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\Debug\\".
          Creando directorio "cmTC_d867d.dir\\Debug\\cmTC_d867d.tlog\\".
        InitializeBuildStatus:
          Se crear├í "cmTC_d867d.dir\\Debug\\cmTC_d867d.tlog\\unsuccessfulbuild" porque se especific├│ "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_d867d.dir\\Debug\\cmTC_d867d.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d867d.dir\\Debug\\\\" /Fd"cmTC_d867d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\src.c"
          Compilador de optimizaci├│n de C/C++ de Microsoft (R) versi├│n 19.44.35211 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d867d.dir\\Debug\\\\" /Fd"cmTC_d867d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\src.c"
          src.c
        F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\src.c(1,10): error C1083: No se puede abrir el archivo incluir: 'pthread.h': No such file or directory [F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\cmTC_d867d.vcxproj]
        Compilaci├│n del proyecto terminada "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\cmTC_d867d.vcxproj" (destinos predeterminados) -- ERROR.
        
        ERROR al compilar.
        
        "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\cmTC_d867d.vcxproj" (destino predeterminado) (1) ->
        (ClCompile destino) -> 
          F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\src.c(1,10): error C1083: No se puede abrir el archivo incluir: 'pthread.h': No such file or directory [F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w3bdf6\\cmTC_d867d.vcxproj]
        
            0 Advertencia(s)
            1 Errores
        
        Tiempo transcurrido 00:00:01.48
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-9afd48"
      binary: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-9afd48"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-9afd48'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_0b1cf.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versi├│n de MSBuild 17.14.10+8b8e13593 para .NET Framework
        Compilaci├│n iniciada a las 21/06/2025 2:16:28ΓÇ»p.ΓÇ»m..
        
        Proyecto "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\cmTC_0b1cf.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_0b1cf.dir\\Debug\\".
          La salida estructurada est├í habilitada. El formato del diagn├│stico del compilador reflejar├í la jerarqu├¡a de errores. Consulte https://aka.ms/cpp/structured-output para obtener m├ís detalles.
          Creando directorio "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\Debug\\".
          Creando directorio "cmTC_0b1cf.dir\\Debug\\cmTC_0b1cf.tlog\\".
        InitializeBuildStatus:
          Se crear├í "cmTC_0b1cf.dir\\Debug\\cmTC_0b1cf.tlog\\unsuccessfulbuild" porque se especific├│ "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_0b1cf.dir\\Debug\\cmTC_0b1cf.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0b1cf.dir\\Debug\\\\" /Fd"cmTC_0b1cf.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\CheckFunctionExists.c"
          Compilador de optimizaci├│n de C/C++ de Microsoft (R) versi├│n 19.44.35211 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0b1cf.dir\\Debug\\\\" /Fd"cmTC_0b1cf.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\Debug\\cmTC_0b1cf.exe" /INCREMENTAL /ILK:"cmTC_0b1cf.dir\\Debug\\cmTC_0b1cf.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-9afd48/Debug/cmTC_0b1cf.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-9afd48/Debug/cmTC_0b1cf.lib" /MACHINE:X64  /machine:x64 cmTC_0b1cf.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: no se puede abrir el archivo 'pthreads.lib' [F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\cmTC_0b1cf.vcxproj]
        Compilaci├│n del proyecto terminada "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\cmTC_0b1cf.vcxproj" (destinos predeterminados) -- ERROR.
        
        ERROR al compilar.
        
        "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\cmTC_0b1cf.vcxproj" (destino predeterminado) (1) ->
        (Link destino) -> 
          LINK : fatal error LNK1104: no se puede abrir el archivo 'pthreads.lib' [F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9afd48\\cmTC_0b1cf.vcxproj]
        
            0 Advertencia(s)
            1 Errores
        
        Tiempo transcurrido 00:00:01.42
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-jjwmdm"
      binary: "F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-jjwmdm"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-jjwmdm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_7f665.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versi├│n de MSBuild 17.14.10+8b8e13593 para .NET Framework
        Compilaci├│n iniciada a las 21/06/2025 2:16:30ΓÇ»p.ΓÇ»m..
        
        Proyecto "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\cmTC_7f665.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_7f665.dir\\Debug\\".
          La salida estructurada est├í habilitada. El formato del diagn├│stico del compilador reflejar├í la jerarqu├¡a de errores. Consulte https://aka.ms/cpp/structured-output para obtener m├ís detalles.
          Creando directorio "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\Debug\\".
          Creando directorio "cmTC_7f665.dir\\Debug\\cmTC_7f665.tlog\\".
        InitializeBuildStatus:
          Se crear├í "cmTC_7f665.dir\\Debug\\cmTC_7f665.tlog\\unsuccessfulbuild" porque se especific├│ "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_7f665.dir\\Debug\\cmTC_7f665.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7f665.dir\\Debug\\\\" /Fd"cmTC_7f665.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\CheckFunctionExists.c"
          Compilador de optimizaci├│n de C/C++ de Microsoft (R) versi├│n 19.44.35211 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7f665.dir\\Debug\\\\" /Fd"cmTC_7f665.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\Debug\\cmTC_7f665.exe" /INCREMENTAL /ILK:"cmTC_7f665.dir\\Debug\\cmTC_7f665.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-jjwmdm/Debug/cmTC_7f665.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/Pattern Weaver (PW)/build/CMakeFiles/CMakeScratch/TryCompile-jjwmdm/Debug/cmTC_7f665.lib" /MACHINE:X64  /machine:x64 cmTC_7f665.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: no se puede abrir el archivo 'pthread.lib' [F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\cmTC_7f665.vcxproj]
        Compilaci├│n del proyecto terminada "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\cmTC_7f665.vcxproj" (destinos predeterminados) -- ERROR.
        
        ERROR al compilar.
        
        "F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\cmTC_7f665.vcxproj" (destino predeterminado) (1) ->
        (Link destino) -> 
          LINK : fatal error LNK1104: no se puede abrir el archivo 'pthread.lib' [F:\\Pattern Weaver (PW)\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jjwmdm\\cmTC_7f665.vcxproj]
        
            0 Advertencia(s)
            1 Errores
        
        Tiempo transcurrido 00:00:01.44
        
      exitCode: 1
...
