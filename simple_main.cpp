#include <iostream>
#include <string>
#include <fstream>
#include <sstream>
#include <vector>
#include <thread>
#include <chrono>
#include <winsock2.h>
#include <ws2tcpip.h>

#pragma comment(lib, "ws2_32.lib")

class SimpleHTTPServer {
private:
    SOCKET server_socket;
    int port;
    bool running;

    std::string read_file(const std::string& path) {
        std::ifstream file(path);
        if (!file.is_open()) {
            return "";
        }
        std::stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    }

    std::string get_content_type(const std::string& path) {
        if (path.ends_with(".html")) return "text/html";
        if (path.ends_with(".css")) return "text/css";
        if (path.ends_with(".js")) return "application/javascript";
        if (path.ends_with(".json")) return "application/json";
        return "text/plain";
    }

    void handle_request(SOCKET client_socket) {
        char buffer[4096];
        int bytes_received = recv(client_socket, buffer, sizeof(buffer) - 1, 0);
        
        if (bytes_received <= 0) {
            closesocket(client_socket);
            return;
        }
        
        buffer[bytes_received] = '\0';
        std::string request(buffer);
        
        // Parse HTTP request
        std::istringstream iss(request);
        std::string method, path, version;
        iss >> method >> path >> version;
        
        std::string response;
        std::string content;
        
        if (path == "/") {
            content = read_file("web/index.html");
            if (content.empty()) {
                response = "HTTP/1.1 404 Not Found\r\nContent-Length: 13\r\n\r\n404 Not Found";
            } else {
                response = "HTTP/1.1 200 OK\r\nContent-Type: text/html\r\nContent-Length: " + 
                          std::to_string(content.length()) + "\r\n\r\n" + content;
            }
        }
        else if (path.starts_with("/static/")) {
            std::string file_path = "web" + path;
            content = read_file(file_path);
            if (content.empty()) {
                response = "HTTP/1.1 404 Not Found\r\nContent-Length: 13\r\n\r\n404 Not Found";
            } else {
                std::string content_type = get_content_type(file_path);
                response = "HTTP/1.1 200 OK\r\nContent-Type: " + content_type + 
                          "\r\nContent-Length: " + std::to_string(content.length()) + "\r\n\r\n" + content;
            }
        }
        else if (path == "/api/test") {
            content = R"({"status": "ok", "message": "MIDI Node Editor is running!"})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " + 
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else {
            response = "HTTP/1.1 404 Not Found\r\nContent-Length: 13\r\n\r\n404 Not Found";
        }
        
        send(client_socket, response.c_str(), response.length(), 0);
        closesocket(client_socket);
    }

public:
    SimpleHTTPServer(int p) : port(p), running(false) {
        WSADATA wsaData;
        WSAStartup(MAKEWORD(2, 2), &wsaData);
    }

    ~SimpleHTTPServer() {
        stop();
        WSACleanup();
    }

    bool start() {
        std::cout << "Creating socket..." << std::endl;
        server_socket = socket(AF_INET, SOCK_STREAM, 0);
        if (server_socket == INVALID_SOCKET) {
            std::cerr << "Failed to create socket: " << WSAGetLastError() << std::endl;
            return false;
        }
        std::cout << "Socket created successfully" << std::endl;

        sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;
        server_addr.sin_port = htons(port);

        if (bind(server_socket, (sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
            std::cerr << "Failed to bind socket" << std::endl;
            closesocket(server_socket);
            return false;
        }

        if (listen(server_socket, SOMAXCONN) == SOCKET_ERROR) {
            std::cerr << "Failed to listen on socket" << std::endl;
            closesocket(server_socket);
            return false;
        }

        running = true;
        std::cout << "Server started on http://localhost:" << port << std::endl;
        
        while (running) {
            sockaddr_in client_addr;
            int client_addr_len = sizeof(client_addr);
            SOCKET client_socket = accept(server_socket, (sockaddr*)&client_addr, &client_addr_len);
            
            if (client_socket != INVALID_SOCKET) {
                std::thread client_thread(&SimpleHTTPServer::handle_request, this, client_socket);
                client_thread.detach();
            }
        }

        return true;
    }

    void stop() {
        running = false;
        if (server_socket != INVALID_SOCKET) {
            closesocket(server_socket);
        }
    }
};

int main() {
    std::cout << "==================================" << std::endl;
    std::cout << "MIDI Node Editor - Simple Server" << std::endl;
    std::cout << "==================================" << std::endl;
    std::cout << std::endl;
    std::cout << "Features:" << std::endl;
    std::cout << "- Beautiful black and purple theme" << std::endl;
    std::cout << "- Node-based interface" << std::endl;
    std::cout << "- MIDI processing capabilities" << std::endl;
    std::cout << "- High-performance architecture" << std::endl;
    std::cout << std::endl;

    SimpleHTTPServer server(8080);
    
    if (!server.start()) {
        std::cerr << "Failed to start server" << std::endl;
        return 1;
    }

    return 0;
}
