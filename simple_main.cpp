#include <iostream>
#include <string>
#include <fstream>
#include <sstream>
#include <vector>
#include <thread>
#include <chrono>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#include <mmsystem.h>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <atomic>
#include <algorithm>
#include <future>
#include <queue>
#include <functional>
#include <condition_variable>
#include <execution>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "winmm.lib")

// Professional Thread Pool for 8+ core systems
class ThreadPool {
private:
    std::vector<std::thread> workers;
    std::queue<std::function<void()>> tasks;
    std::mutex queue_mutex;
    std::condition_variable condition;
    std::atomic<bool> stop{false};

public:
    ThreadPool(size_t threads = std::thread::hardware_concurrency()) {
        // Use all available cores for professional performance
        if (threads < 8) threads = 8; // Minimum 8 threads for professional use

        for (size_t i = 0; i < threads; ++i) {
            workers.emplace_back([this] {
                for (;;) {
                    std::function<void()> task;
                    {
                        std::unique_lock<std::mutex> lock(this->queue_mutex);
                        this->condition.wait(lock, [this] { return this->stop || !this->tasks.empty(); });

                        if (this->stop && this->tasks.empty()) return;

                        task = std::move(this->tasks.front());
                        this->tasks.pop();
                    }
                    task();
                }
            });
        }
    }

    template<class F, class... Args>
    auto enqueue(F&& f, Args&&... args) -> std::future<typename std::result_of<F(Args...)>::type> {
        using return_type = typename std::result_of<F(Args...)>::type;

        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );

        std::future<return_type> res = task->get_future();
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            if (stop) throw std::runtime_error("enqueue on stopped ThreadPool");
            tasks.emplace([task]() { (*task)(); });
        }
        condition.notify_one();
        return res;
    }

    ~ThreadPool() {
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            stop = true;
        }
        condition.notify_all();
        for (std::thread &worker : workers) {
            worker.join();
        }
    }
};

// High-performance MIDI data structures
struct MidiNote {
    uint32_t start_tick;
    uint32_t end_tick;
    uint8_t channel;
    uint8_t note;
    uint8_t velocity;
    uint16_t track_id;

    bool operator<(const MidiNote& other) const {
        return start_tick < other.start_tick;
    }
};

struct MidiEvent {
    uint32_t tick;
    uint8_t type;
    uint8_t channel;
    std::vector<uint8_t> data;
    uint16_t track_id;
};

// High-performance storage for millions of notes with multithreading
class MidiDataStore {
private:
    std::vector<MidiNote> notes;
    std::vector<MidiEvent> events;
    mutable std::mutex data_mutex; // Mutex for thread safety
    std::atomic<bool> is_loading{false};
    uint16_t ticks_per_quarter = 480;
    ThreadPool* thread_pool;

public:
    MidiDataStore(ThreadPool* pool = nullptr) : thread_pool(pool) {}

    void reserve_capacity(size_t note_count, size_t event_count) {
        std::lock_guard<std::mutex> lock(data_mutex);
        notes.reserve(note_count);
        events.reserve(event_count);
    }

    void add_note(const MidiNote& note) {
        std::lock_guard<std::mutex> lock(data_mutex);
        notes.push_back(note);
    }

    void add_event(const MidiEvent& event) {
        std::lock_guard<std::mutex> lock(data_mutex);
        events.push_back(event);
    }

    void optimize_for_playback() {
        std::lock_guard<std::mutex> lock(data_mutex);
        // Use parallel sort for large datasets (if available)
        std::sort(notes.begin(), notes.end());
    }

    std::vector<MidiNote> get_notes_in_range(uint32_t start_tick, uint32_t end_tick) const {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(data_mutex));
        std::vector<MidiNote> result;

        // Use binary search for optimized range queries on sorted data
        auto start_it = std::lower_bound(notes.begin(), notes.end(),
            MidiNote{start_tick, 0, 0, 0, 0, 0});
        auto end_it = std::upper_bound(notes.begin(), notes.end(),
            MidiNote{end_tick, UINT32_MAX, 0, 0, 0, 0});

        result.reserve(std::distance(start_it, end_it));
        for (auto it = start_it; it != end_it; ++it) {
            if (it->start_tick >= start_tick && it->start_tick <= end_tick) {
                result.push_back(*it);
            }
        }
        return result;
    }

    size_t get_note_count() const {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(data_mutex));
        return notes.size();
    }

    void clear() {
        std::lock_guard<std::mutex> lock(data_mutex);
        notes.clear();
        events.clear();
    }

    std::string to_json_range(uint32_t start_tick, uint32_t end_tick, size_t max_notes = 10000) const {
        auto range_notes = get_notes_in_range(start_tick, end_tick);

        if (range_notes.size() > max_notes) {
            range_notes.resize(max_notes);
        }

        std::ostringstream json;
        json << "{\"notes\":[";
        for (size_t i = 0; i < range_notes.size(); ++i) {
            if (i > 0) json << ",";
            const auto& note = range_notes[i];
            json << "{\"start\":" << note.start_tick
                 << ",\"end\":" << note.end_tick
                 << ",\"channel\":" << (int)note.channel
                 << ",\"note\":" << (int)note.note
                 << ",\"velocity\":" << (int)note.velocity
                 << ",\"track\":" << note.track_id << "}";
        }
        json << "],\"total_notes\":" << get_note_count()
             << ",\"displayed_notes\":" << range_notes.size()
             << ",\"start_tick\":" << start_tick
             << ",\"end_tick\":" << end_tick << "}";

        return json.str();
    }
};

// Windows MIDI Output
class WindowsMidiOutput {
private:
    HMIDIOUT midi_out;

public:
    bool is_open;
    WindowsMidiOutput() : midi_out(nullptr), is_open(false) {}

    ~WindowsMidiOutput() {
        close();
    }

    bool open(int device_id = MIDI_MAPPER) {
        MMRESULT result = midiOutOpen(&midi_out, device_id, 0, 0, CALLBACK_NULL);
        is_open = (result == MMSYSERR_NOERROR);
        return is_open;
    }

    void close() {
        if (is_open && midi_out) {
            midiOutClose(midi_out);
            midi_out = nullptr;
            is_open = false;
        }
    }

    void send_note_on(uint8_t channel, uint8_t note, uint8_t velocity) {
        if (!is_open) return;
        DWORD message = 0x90 | channel | (note << 8) | (velocity << 16);
        midiOutShortMsg(midi_out, message);
    }

    void send_note_off(uint8_t channel, uint8_t note) {
        if (!is_open) return;
        DWORD message = 0x80 | channel | (note << 8);
        midiOutShortMsg(midi_out, message);
    }

    void all_notes_off() {
        if (!is_open) return;
        for (int channel = 0; channel < 16; ++channel) {
            DWORD message = 0xB0 | channel | (0x7B << 8);
            midiOutShortMsg(midi_out, message);
        }
    }
};

// High-Performance Multithreaded MIDI Parser
class FastMidiParser {
private:
    ThreadPool* thread_pool;

public:
    FastMidiParser(ThreadPool* pool = nullptr) : thread_pool(pool) {}

    struct ParseResult {
        std::vector<MidiNote> notes;
        std::vector<MidiEvent> events;
        bool success = false;
        std::string error_message;
        double parse_time_ms = 0.0;
        uint16_t ticks_per_quarter = 480;
    };

    ParseResult parse_file(const std::string& filename) {
        ParseResult result;
        auto start_time = std::chrono::high_resolution_clock::now();

        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            result.error_message = "Could not open file: " + filename;
            return result;
        }

        // Read entire file into memory for fast parsing
        file.seekg(0, std::ios::end);
        size_t file_size = file.tellg();
        file.seekg(0, std::ios::beg);

        std::vector<uint8_t> data(file_size);
        file.read(reinterpret_cast<char*>(data.data()), file_size);
        file.close();

        // Parse MIDI header
        if (file_size < 14 ||
            data[0] != 'M' || data[1] != 'T' || data[2] != 'h' || data[3] != 'd') {
            result.error_message = "Invalid MIDI file format";
            return result;
        }

        // Extract header info
        uint16_t track_count = (data[10] << 8) | data[11];
        result.ticks_per_quarter = (data[12] << 8) | data[13];

        // Parse tracks in parallel for professional performance
        size_t pos = 14;
        std::vector<std::future<std::pair<std::vector<MidiNote>, std::vector<MidiEvent>>>> track_futures;

        for (uint16_t track = 0; track < track_count && pos < file_size; ++track) {
            if (pos + 8 > file_size) break;

            // Check track header
            if (data[pos] != 'M' || data[pos+1] != 'T' ||
                data[pos+2] != 'r' || data[pos+3] != 'k') {
                continue;
            }

            uint32_t track_length = (data[pos+4] << 24) | (data[pos+5] << 16) |
                                   (data[pos+6] << 8) | data[pos+7];
            pos += 8;

            // Parse tracks in parallel if thread pool is available
            if (thread_pool && track_count > 1) {
                auto future = thread_pool->enqueue([this, data, pos, track_length, track]() {
                    return parse_track_parallel(data, pos, pos + track_length, track);
                });
                track_futures.push_back(std::move(future));
            } else {
                parse_track(data, pos, pos + track_length, track, result);
            }
            pos += track_length;
        }

        // Collect results from parallel parsing
        if (!track_futures.empty()) {
            for (auto& future : track_futures) {
                auto track_result = future.get();
                result.notes.insert(result.notes.end(), track_result.first.begin(), track_result.first.end());
                result.events.insert(result.events.end(), track_result.second.begin(), track_result.second.end());
            }
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        result.parse_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        result.success = true;

        return result;
    }

private:
    // Parallel track parsing for high performance
    std::pair<std::vector<MidiNote>, std::vector<MidiEvent>> parse_track_parallel(
        const std::vector<uint8_t>& data, size_t start, size_t end, uint16_t track_id) {

        std::vector<MidiNote> track_notes;
        std::vector<MidiEvent> track_events;

        size_t pos = start;
        uint32_t current_tick = 0;
        uint8_t running_status = 0;
        std::unordered_map<uint8_t, uint32_t> note_on_times[16]; // Per channel

        while (pos < end) {
            // Read variable-length delta time
            uint32_t delta_time = 0;
            while (pos < end && (data[pos] & 0x80)) {
                delta_time = (delta_time << 7) | (data[pos] & 0x7F);
                pos++;
            }
            if (pos < end) {
                delta_time = (delta_time << 7) | data[pos];
                pos++;
            }
            current_tick += delta_time;

            if (pos >= end) break;

            uint8_t status = data[pos];
            if (status < 0x80) {
                status = running_status;
            } else {
                pos++;
                running_status = status;
            }

            uint8_t type = status & 0xF0;
            uint8_t channel = status & 0x0F;

            if (type == 0x90 && pos + 1 < end) { // Note On
                uint8_t note = data[pos];
                uint8_t velocity = data[pos + 1];
                pos += 2;

                if (velocity > 0) {
                    note_on_times[channel][note] = current_tick;
                } else {
                    // Velocity 0 = Note Off
                    auto it = note_on_times[channel].find(note);
                    if (it != note_on_times[channel].end()) {
                        MidiNote midi_note;
                        midi_note.start_tick = it->second;
                        midi_note.end_tick = current_tick;
                        midi_note.channel = channel;
                        midi_note.note = note;
                        midi_note.velocity = 64;
                        midi_note.track_id = track_id;
                        track_notes.push_back(midi_note);
                        note_on_times[channel].erase(it);
                    }
                }
            } else if (type == 0x80 && pos + 1 < end) { // Note Off
                uint8_t note = data[pos];
                pos += 2;

                auto it = note_on_times[channel].find(note);
                if (it != note_on_times[channel].end()) {
                    MidiNote midi_note;
                    midi_note.start_tick = it->second;
                    midi_note.end_tick = current_tick;
                    midi_note.channel = channel;
                    midi_note.note = note;
                    midi_note.velocity = 64;
                    midi_note.track_id = track_id;
                    track_notes.push_back(midi_note);
                    note_on_times[channel].erase(it);
                }
            } else if (type == 0xB0 || type == 0xC0 || type == 0xE0) {
                pos += (type == 0xC0) ? 1 : 2;
            } else if (status == 0xFF && pos < end) { // Meta event
                pos++;
                uint32_t length = 0;
                while (pos < end && (data[pos] & 0x80)) {
                    length = (length << 7) | (data[pos] & 0x7F);
                    pos++;
                }
                if (pos < end) {
                    length = (length << 7) | data[pos];
                    pos++;
                }
                pos += length;
            } else {
                pos++;
            }
        }

        return std::make_pair(std::move(track_notes), std::move(track_events));
    }

    void parse_track(const std::vector<uint8_t>& data, size_t start, size_t end,
                    uint16_t track_id, ParseResult& result) {
        size_t pos = start;
        uint32_t current_tick = 0;
        uint8_t running_status = 0;
        std::unordered_map<uint8_t, uint32_t> note_on_times[16]; // Per channel

        while (pos < end) {
            // Read variable-length delta time
            uint32_t delta_time = 0;
            while (pos < end && (data[pos] & 0x80)) {
                delta_time = (delta_time << 7) | (data[pos] & 0x7F);
                pos++;
            }
            if (pos < end) {
                delta_time = (delta_time << 7) | data[pos];
                pos++;
            }
            current_tick += delta_time;

            if (pos >= end) break;

            uint8_t status = data[pos];
            if (status < 0x80) {
                status = running_status;
            } else {
                pos++;
                running_status = status;
            }

            uint8_t type = status & 0xF0;
            uint8_t channel = status & 0x0F;

            if (type == 0x90 && pos + 1 < end) { // Note On
                uint8_t note = data[pos];
                uint8_t velocity = data[pos + 1];
                pos += 2;

                if (velocity > 0) {
                    note_on_times[channel][note] = current_tick;
                } else {
                    // Velocity 0 = Note Off
                    auto it = note_on_times[channel].find(note);
                    if (it != note_on_times[channel].end()) {
                        MidiNote midi_note;
                        midi_note.start_tick = it->second;
                        midi_note.end_tick = current_tick;
                        midi_note.channel = channel;
                        midi_note.note = note;
                        midi_note.velocity = 64; // Default velocity for note off
                        midi_note.track_id = track_id;
                        result.notes.push_back(midi_note);
                        note_on_times[channel].erase(it);
                    }
                }
            } else if (type == 0x80 && pos + 1 < end) { // Note Off
                uint8_t note = data[pos];
                pos += 2; // Skip velocity

                auto it = note_on_times[channel].find(note);
                if (it != note_on_times[channel].end()) {
                    MidiNote midi_note;
                    midi_note.start_tick = it->second;
                    midi_note.end_tick = current_tick;
                    midi_note.channel = channel;
                    midi_note.note = note;
                    midi_note.velocity = 64;
                    midi_note.track_id = track_id;
                    result.notes.push_back(midi_note);
                    note_on_times[channel].erase(it);
                }
            } else if (type == 0xB0 || type == 0xC0 || type == 0xE0) {
                // Control Change, Program Change, Pitch Bend
                pos += (type == 0xC0) ? 1 : 2;
            } else if (status == 0xFF && pos < end) { // Meta event
                pos++; // Skip meta type
                uint32_t length = 0;
                while (pos < end && (data[pos] & 0x80)) {
                    length = (length << 7) | (data[pos] & 0x7F);
                    pos++;
                }
                if (pos < end) {
                    length = (length << 7) | data[pos];
                    pos++;
                }
                pos += length;
            } else {
                pos++; // Skip unknown events
            }
        }
    }
};

class SimpleHTTPServer {
private:
    SOCKET server_socket;
    int port;
    bool running;
    ThreadPool thread_pool;
    MidiDataStore midi_store;
    WindowsMidiOutput midi_output;
    FastMidiParser midi_parser;

    std::string read_file(const std::string& path) {
        std::ifstream file(path);
        if (!file.is_open()) {
            return "";
        }
        std::stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    }

    std::string get_content_type(const std::string& path) {
        if (path.length() >= 5 && path.substr(path.length() - 5) == ".html") return "text/html";
        if (path.length() >= 4 && path.substr(path.length() - 4) == ".css") return "text/css";
        if (path.length() >= 3 && path.substr(path.length() - 3) == ".js") return "application/javascript";
        if (path.length() >= 5 && path.substr(path.length() - 5) == ".json") return "application/json";
        return "text/plain";
    }

    void handle_request(SOCKET client_socket) {
        char buffer[4096];
        int bytes_received = recv(client_socket, buffer, sizeof(buffer) - 1, 0);

        if (bytes_received <= 0) {
            closesocket(client_socket);
            return;
        }

        buffer[bytes_received] = '\0';
        std::string request(buffer);

        // Log incoming requests
        std::string first_line = request.substr(0, request.find('\n'));
        std::cout << "Request: " << first_line << std::endl;
        
        // Parse HTTP request
        std::istringstream iss(request);
        std::string method, path, version;
        iss >> method >> path >> version;
        
        std::string response;
        std::string content;

        std::cout << "Request: " << method << " " << path << std::endl;

        if (path == "/") {
            content = read_file("web/index.html");
            if (content.empty()) {
                response = "HTTP/1.1 404 Not Found\r\nContent-Length: 13\r\n\r\n404 Not Found";
            } else {
                response = "HTTP/1.1 200 OK\r\nContent-Type: text/html\r\nContent-Length: " + 
                          std::to_string(content.length()) + "\r\n\r\n" + content;
            }
        }
        else if (path.length() >= 8 && path.substr(0, 8) == "/static/") {
            std::string file_path = "web" + path;
            content = read_file(file_path);
            if (content.empty()) {
                response = "HTTP/1.1 404 Not Found\r\nContent-Length: 13\r\n\r\n404 Not Found";
            } else {
                std::string content_type = get_content_type(file_path);
                response = "HTTP/1.1 200 OK\r\nContent-Type: " + content_type + 
                          "\r\nContent-Length: " + std::to_string(content.length()) + "\r\n\r\n" + content;
            }
        }
        else if (path == "/api/test") {
            content = R"({"status": "ok", "message": "Pattern Weaver is running!", "timestamp": ")" + std::to_string(time(nullptr)) + R"("})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/midi-stats") {
            std::ostringstream json;
            json << "{\"total_notes\":" << midi_store.get_note_count()
                 << ",\"memory_usage_mb\":" << (midi_store.get_note_count() * sizeof(MidiNote)) / (1024 * 1024)
                 << ",\"midi_output_open\":" << (midi_output.is_open ? "true" : "false") << "}";
            content = json.str();
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path.length() >= 16 && path.substr(0, 16) == "/api/midi-range") {
            // Parse query parameters
            uint32_t start_tick = 0, end_tick = 1000;
            size_t max_notes = 10000;

            size_t query_pos = path.find('?');
            if (query_pos != std::string::npos) {
                std::string query = path.substr(query_pos + 1);
                // Simple parameter parsing
                if (query.find("start=") != std::string::npos) {
                    start_tick = std::stoul(query.substr(query.find("start=") + 6));
                }
                if (query.find("end=") != std::string::npos) {
                    end_tick = std::stoul(query.substr(query.find("end=") + 4));
                }
                if (query.find("max=") != std::string::npos) {
                    max_notes = std::stoul(query.substr(query.find("max=") + 4));
                }
            }

            content = midi_store.to_json_range(start_tick, end_tick, max_notes);
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/midi-play-note" && method == "POST") {
            // Parse JSON body for note playing
            std::string body = request.substr(request.find("\r\n\r\n") + 4);
            // Simple JSON parsing for demo
            if (body.find("\"note\":") != std::string::npos) {
                size_t note_pos = body.find("\"note\":") + 7;
                uint8_t note = static_cast<uint8_t>(std::stoi(body.substr(note_pos)));
                uint8_t velocity = 100;
                uint8_t channel = 0;

                if (body.find("\"velocity\":") != std::string::npos) {
                    size_t vel_pos = body.find("\"velocity\":") + 11;
                    velocity = static_cast<uint8_t>(std::stoi(body.substr(vel_pos)));
                }

                midi_output.send_note_on(channel, note, velocity);

                // Auto note-off after 500ms
                std::thread([this, channel, note]() {
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    midi_output.send_note_off(channel, note);
                }).detach();
            }

            content = R"({"success": true})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/midi-load" && method == "POST") {
            // Load MIDI file
            std::string body = request.substr(request.find("\r\n\r\n") + 4);
            std::string filename = "uploads/test.mid"; // Default for demo

            // Parse filename from JSON body
            if (body.find("\"filename\":") != std::string::npos) {
                size_t filename_pos = body.find("\"filename\":") + 12;
                size_t filename_start = body.find("\"", filename_pos) + 1;
                size_t filename_end = body.find("\"", filename_start);
                if (filename_end != std::string::npos) {
                    std::string requested_filename = body.substr(filename_start, filename_end - filename_start);
                    filename = "uploads/" + requested_filename;
                }
            }

            std::cout << "Loading MIDI file: " << filename << std::endl;

            // Check if file exists
            std::ifstream test_file(filename);
            if (!test_file.good()) {
                std::cout << "File does not exist: " << filename << std::endl;
                content = R"({"success":false,"error":"MIDI file not found"})";
            } else {
                test_file.close();

                auto parse_result = midi_parser.parse_file(filename);
                if (parse_result.success) {
                    midi_store.clear();
                    midi_store.reserve_capacity(parse_result.notes.size(), parse_result.events.size());

                    for (const auto& note : parse_result.notes) {
                        midi_store.add_note(note);
                    }

                    midi_store.optimize_for_playback();

                    std::ostringstream json;
                    json << "{\"success\":true,\"note_count\":" << parse_result.notes.size()
                         << ",\"parse_time_ms\":" << parse_result.parse_time_ms
                         << ",\"filename\":\"" << filename << "\"}";
                    content = json.str();

                    std::cout << "MIDI loaded successfully: " << parse_result.notes.size() << " notes" << std::endl;
                } else {
                    std::ostringstream json;
                    json << "{\"success\":false,\"error\":\"" << parse_result.error_message << "\"}";
                    content = json.str();

                    std::cout << "MIDI load failed: " << parse_result.error_message << std::endl;
                }
            }

            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/midi-stop-note" && method == "POST") {
            // Parse JSON body for note stopping
            std::string body = request.substr(request.find("\r\n\r\n") + 4);
            if (body.find("\"note\":") != std::string::npos) {
                size_t note_pos = body.find("\"note\":") + 7;
                uint8_t note = static_cast<uint8_t>(std::stoi(body.substr(note_pos)));
                uint8_t channel = 0;

                midi_output.send_note_off(channel, note);
            }

            content = R"({"success": true})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/upload-midi" && method == "POST") {
            std::cout << "Received file upload request" << std::endl;

            // Read the full request body for multipart data
            std::string body = request.substr(request.find("\r\n\r\n") + 4);
            std::cout << "Request body size: " << body.length() << " bytes" << std::endl;

            // For now, just return success - we'll use the test file
            // In a real implementation, you'd parse the multipart form data properly
            content = R"({"success": true, "message": "File upload simulated", "filename": "test.mid"})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;

            std::cout << "Upload response sent" << std::endl;
        }
        // Node management endpoints
        else if (path == "/api/nodes" && method == "POST") {
            content = R"({"success": true, "id": 1})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path.length() >= 11 && path.substr(0, 11) == "/api/nodes/" && method == "DELETE") {
            content = R"({"success": true})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/connections" && method == "POST") {
            content = R"({"success": true, "id": 1})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path.length() >= 17 && path.substr(0, 17) == "/api/connections/" && method == "DELETE") {
            content = R"({"success": true})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/graph") {
            content = R"({"nodes": [], "connections": []})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/graph" && method == "DELETE") {
            content = R"({"success": true})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/save" && method == "POST") {
            content = R"({"success": true})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        else if (path == "/api/load" && method == "POST") {
            content = R"({"success": true})";
            response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nAccess-Control-Allow-Origin: *\r\nContent-Length: " +
                      std::to_string(content.length()) + "\r\n\r\n" + content;
        }
        // Handle CORS preflight requests
        else if (method == "OPTIONS") {
            response = "HTTP/1.1 200 OK\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, DELETE, OPTIONS\r\nAccess-Control-Allow-Headers: Content-Type, Authorization\r\nContent-Length: 0\r\n\r\n";
        }
        else {
            std::cout << "404 - Path not found: " << path << std::endl;
            response = "HTTP/1.1 404 Not Found\r\nContent-Length: 13\r\n\r\n404 Not Found";
        }
        
        send(client_socket, response.c_str(), static_cast<int>(response.length()), 0);
        closesocket(client_socket);
    }

public:
    SimpleHTTPServer(int p) : port(p), running(false),
                             midi_store(&thread_pool),
                             midi_parser(&thread_pool) {
        WSADATA wsaData;
        WSAStartup(MAKEWORD(2, 2), &wsaData);

        std::cout << "Professional Thread Pool initialized with "
                  << std::thread::hardware_concurrency() << " threads" << std::endl;

        // Initialize MIDI output
        if (midi_output.open()) {
            std::cout << "MIDI output initialized successfully" << std::endl;
        } else {
            std::cout << "Warning: Could not initialize MIDI output" << std::endl;
        }
    }

    ~SimpleHTTPServer() {
        stop();
        WSACleanup();
    }

    bool start() {
        std::cout << "Creating socket..." << std::endl;
        server_socket = socket(AF_INET, SOCK_STREAM, 0);
        if (server_socket == INVALID_SOCKET) {
            std::cerr << "Failed to create socket: " << WSAGetLastError() << std::endl;
            return false;
        }
        std::cout << "Socket created successfully" << std::endl;

        sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = INADDR_ANY;
        server_addr.sin_port = htons(static_cast<u_short>(port));

        if (bind(server_socket, (sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
            std::cerr << "Failed to bind socket" << std::endl;
            closesocket(server_socket);
            return false;
        }

        if (listen(server_socket, SOMAXCONN) == SOCKET_ERROR) {
            std::cerr << "Failed to listen on socket" << std::endl;
            closesocket(server_socket);
            return false;
        }

        std::cout << "Server listening on http://localhost:" << port << std::endl;
        std::cout << "Ready to accept connections..." << std::endl;

        running = true;
        std::cout << "Server started on http://localhost:" << port << std::endl;
        
        while (running) {
            sockaddr_in client_addr;
            int client_addr_len = sizeof(client_addr);
            SOCKET client_socket = accept(server_socket, (sockaddr*)&client_addr, &client_addr_len);
            
            if (client_socket != INVALID_SOCKET) {
                std::thread client_thread(&SimpleHTTPServer::handle_request, this, client_socket);
                client_thread.detach();
            }
        }

        return true;
    }

    void stop() {
        running = false;
        if (server_socket != INVALID_SOCKET) {
            closesocket(server_socket);
        }
    }
};

int main() {
    std::cout << "==================================" << std::endl;
    std::cout << "Pattern Weaver - MIDI Node Editor" << std::endl;
    std::cout << "==================================" << std::endl;
    std::cout << "Server starting on port 8080..." << std::endl;
    std::cout << std::endl;
    std::cout << "Features:" << std::endl;
    std::cout << "- Beautiful black and purple theme" << std::endl;
    std::cout << "- Node-based interface" << std::endl;
    std::cout << "- MIDI processing capabilities" << std::endl;
    std::cout << "- High-performance architecture" << std::endl;
    std::cout << std::endl;

    SimpleHTTPServer server(8080);
    
    if (!server.start()) {
        std::cerr << "Failed to start server" << std::endl;
        return 1;
    }

    return 0;
}
