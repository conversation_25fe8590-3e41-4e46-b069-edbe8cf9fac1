// Node Components for Rete.js

class InputComponent extends Rete.Component {
    constructor() {
        super("Input");
    }

    builder(node) {
        const out1 = new Rete.Output("value", "Value", numSocket);
        
        return node
            .addOutput(out1)
            .addControl(new TextControl(this.editor, "value", "Input Value"));
    }

    worker(node, inputs, outputs) {
        const value = node.data.value || 0;
        outputs["value"] = value;
    }
}

class ProcessComponent extends Rete.Component {
    constructor() {
        super("Process");
    }

    builder(node) {
        const inp1 = new Rete.Input("input1", "Input 1", numSocket);
        const inp2 = new Rete.Input("input2", "Input 2", numSocket);
        const out = new Rete.Output("result", "Result", numSocket);
        
        return node
            .addInput(inp1)
            .addInput(inp2)
            .addOutput(out)
            .addControl(new SelectControl(this.editor, "operation", "Operation", ["add", "subtract", "multiply", "divide"]));
    }

    worker(node, inputs, outputs) {
        const input1 = inputs["input1"] ? inputs["input1"][0] : 0;
        const input2 = inputs["input2"] ? inputs["input2"][0] : 0;
        const operation = node.data.operation || "add";
        
        let result = 0;
        switch (operation) {
            case "add":
                result = input1 + input2;
                break;
            case "subtract":
                result = input1 - input2;
                break;
            case "multiply":
                result = input1 * input2;
                break;
            case "divide":
                result = input2 !== 0 ? input1 / input2 : 0;
                break;
        }
        
        outputs["result"] = result;
    }
}

class OutputComponent extends Rete.Component {
    constructor() {
        super("Output");
    }

    builder(node) {
        const inp = new Rete.Input("value", "Value", numSocket);
        
        return node
            .addInput(inp)
            .addControl(new DisplayControl(this.editor, "display", "Output"));
    }

    worker(node, inputs, outputs) {
        const value = inputs["value"] ? inputs["value"][0] : 0;
        node.data.display = value;
    }
}

// Custom Controls
class TextControl extends Rete.Control {
    constructor(emitter, key, label, readonly = false) {
        super(key);
        this.component = {
            props: ['readonly', 'emitter', 'ikey', 'getData', 'putData', 'label'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <input 
                        type="text" 
                        :readonly="readonly" 
                        :value="value" 
                        @input="change($event)" 
                        @dblclick.stop="" 
                        @pointerdown.stop="" 
                        @pointermove.stop=""
                    />
                </div>
            `,
            data() {
                return {
                    value: this.getData(this.ikey) || ""
                }
            },
            methods: {
                change(e) {
                    this.value = e.target.value;
                    this.putData(this.ikey, this.value);
                    this.emitter.trigger('process');
                }
            }
        };
        this.props = { emitter, ikey: key, readonly, label };
    }
}

class SelectControl extends Rete.Control {
    constructor(emitter, key, label, options) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label', 'options'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <select 
                        :value="value" 
                        @change="change($event)"
                        @pointerdown.stop="" 
                        @pointermove.stop=""
                    >
                        <option v-for="option in options" :key="option" :value="option">
                            {{ option }}
                        </option>
                    </select>
                </div>
            `,
            data() {
                return {
                    value: this.getData(this.ikey) || this.options[0]
                }
            },
            methods: {
                change(e) {
                    this.value = e.target.value;
                    this.putData(this.ikey, this.value);
                    this.emitter.trigger('process');
                }
            }
        };
        this.props = { emitter, ikey: key, label, options };
    }
}

class DisplayControl extends Rete.Control {
    constructor(emitter, key, label) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <div class="display-value">{{ value }}</div>
                </div>
            `,
            data() {
                return {
                    value: this.getData(this.ikey) || 0
                }
            },
            mounted() {
                this.emitter.on('process', () => {
                    this.value = this.getData(this.ikey) || 0;
                });
            }
        };
        this.props = { emitter, ikey: key, label };
    }
}

// Socket definition
const numSocket = new Rete.Socket("Number value");

// Export components
window.NodeComponents = {
    InputComponent,
    ProcessComponent,
    OutputComponent,
    numSocket
};
