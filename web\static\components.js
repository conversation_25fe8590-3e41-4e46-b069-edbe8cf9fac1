// MIDI Node Components for Rete.js

// Socket definitions
const midiSocket = new Rete.Socket("MIDI data");
const numSocket = new Rete.Socket("Number value");

class MidiFileInputComponent extends Rete.Component {
    constructor() {
        super("MIDI File Input");
    }

    builder(node) {
        const out1 = new Rete.Output("midi", "MIDI Data", midiSocket);

        return node
            .addOutput(out1)
            .addControl(new FileControl(this.editor, "filename", "MIDI File"))
            .addControl(new DisplayControl(this.editor, "stats", "File Stats"));
    }

    worker(node, inputs, outputs) {
        const filename = node.data.filename || "";
        if (filename) {
            // This would trigger a backend call to load the MIDI file
            outputs["midi"] = { type: "midi_file", filename: filename };
        }
    }
}

class MidiFilterComponent extends Rete.Component {
    constructor() {
        super("MIDI Filter");
    }

    builder(node) {
        const inp1 = new Rete.Input("midi", "MIDI Input", midiSocket);
        const out1 = new Rete.Output("midi", "Filtered MIDI", midiSocket);

        return node
            .addInput(inp1)
            .addOutput(out1)
            .addControl(new SelectControl(this.editor, "filter_type", "Filter Type",
                ["channel", "note_range", "time_range", "velocity_range"]))
            .addControl(new RangeControl(this.editor, "min_value", "Min Value", 0, 127))
            .addControl(new RangeControl(this.editor, "max_value", "Max Value", 0, 127));
    }

    worker(node, inputs, outputs) {
        const midi_input = inputs["midi"] ? inputs["midi"][0] : null;
        if (midi_input) {
            const filter_type = node.data.filter_type || "channel";
            const min_val = node.data.min_value || 0;
            const max_val = node.data.max_value || 127;

            outputs["midi"] = {
                type: "filtered_midi",
                source: midi_input,
                filter: { type: filter_type, min: min_val, max: max_val }
            };
        }
    }
}

class MidiTransformComponent extends Rete.Component {
    constructor() {
        super("MIDI Transform");
    }

    builder(node) {
        const inp1 = new Rete.Input("midi", "MIDI Input", midiSocket);
        const out1 = new Rete.Output("midi", "Transformed MIDI", midiSocket);

        return node
            .addInput(inp1)
            .addOutput(out1)
            .addControl(new SelectControl(this.editor, "transform_type", "Transform",
                ["transpose", "velocity_scale", "time_stretch", "quantize"]))
            .addControl(new NumberControl(this.editor, "amount", "Amount", -24, 24))
            .addControl(new NumberControl(this.editor, "factor", "Factor", 0.1, 10.0, 0.1));
    }

    worker(node, inputs, outputs) {
        const midi_input = inputs["midi"] ? inputs["midi"][0] : null;
        if (midi_input) {
            const transform_type = node.data.transform_type || "transpose";
            const amount = node.data.amount || 0;
            const factor = node.data.factor || 1.0;

            outputs["midi"] = {
                type: "transformed_midi",
                source: midi_input,
                transform: { type: transform_type, amount: amount, factor: factor }
            };
        }
    }
}

class MidiVisualizerComponent extends Rete.Component {
    constructor() {
        super("MIDI Visualizer");
    }

    builder(node) {
        const inp1 = new Rete.Input("midi", "MIDI Input", midiSocket);

        return node
            .addInput(inp1)
            .addControl(new SelectControl(this.editor, "view_type", "View Type",
                ["piano_roll", "note_histogram", "velocity_graph", "timeline"]))
            .addControl(new RangeControl(this.editor, "start_time", "Start Time (s)", 0, 3600))
            .addControl(new RangeControl(this.editor, "end_time", "End Time (s)", 0, 3600))
            .addControl(new VisualizerControl(this.editor, "display", "Visualization"));
    }

    worker(node, inputs, outputs) {
        const midi_input = inputs["midi"] ? inputs["midi"][0] : null;
        if (midi_input) {
            const view_type = node.data.view_type || "piano_roll";
            const start_time = node.data.start_time || 0;
            const end_time = node.data.end_time || 60;

            // This would trigger visualization rendering
            node.data.display = {
                type: view_type,
                source: midi_input,
                time_range: [start_time, end_time]
            };
        }
    }
}

class MidiOutputComponent extends Rete.Component {
    constructor() {
        super("MIDI Output");
    }

    builder(node) {
        const inp1 = new Rete.Input("midi", "MIDI Input", midiSocket);

        return node
            .addInput(inp1)
            .addControl(new TextControl(this.editor, "output_filename", "Output File"))
            .addControl(new SelectControl(this.editor, "format", "Format",
                ["midi", "fast_format", "json", "csv"]))
            .addControl(new ButtonControl(this.editor, "export", "Export File"));
    }

    worker(node, inputs, outputs) {
        const midi_input = inputs["midi"] ? inputs["midi"][0] : null;
        if (midi_input && node.data.export_triggered) {
            const filename = node.data.output_filename || "output.mid";
            const format = node.data.format || "midi";

            // This would trigger file export
            console.log("Exporting MIDI to:", filename, "in format:", format);
            node.data.export_triggered = false;
        }
    }
}

// Removed unused basic components (InputComponent, ProcessComponent, OutputComponent)
// Keeping only MIDI-specific components for professional use

// Custom Controls
class TextControl extends Rete.Control {
    constructor(emitter, key, label, readonly = false) {
        super(key);
        this.component = {
            props: ['readonly', 'emitter', 'ikey', 'getData', 'putData', 'label'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <input 
                        type="text" 
                        :readonly="readonly" 
                        :value="value" 
                        @input="change($event)" 
                        @dblclick.stop="" 
                        @pointerdown.stop="" 
                        @pointermove.stop=""
                    />
                </div>
            `,
            data() {
                return {
                    value: this.getData(this.ikey) || ""
                }
            },
            methods: {
                change(e) {
                    this.value = e.target.value;
                    this.putData(this.ikey, this.value);
                    this.emitter.trigger('process');
                }
            }
        };
        this.props = { emitter, ikey: key, readonly, label };
    }
}

class SelectControl extends Rete.Control {
    constructor(emitter, key, label, options) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label', 'options'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <select 
                        :value="value" 
                        @change="change($event)"
                        @pointerdown.stop="" 
                        @pointermove.stop=""
                    >
                        <option v-for="option in options" :key="option" :value="option">
                            {{ option }}
                        </option>
                    </select>
                </div>
            `,
            data() {
                return {
                    value: this.getData(this.ikey) || this.options[0]
                }
            },
            methods: {
                change(e) {
                    this.value = e.target.value;
                    this.putData(this.ikey, this.value);
                    this.emitter.trigger('process');
                }
            }
        };
        this.props = { emitter, ikey: key, label, options };
    }
}

class DisplayControl extends Rete.Control {
    constructor(emitter, key, label) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <div class="display-value">{{ value }}</div>
                </div>
            `,
            data() {
                return {
                    value: this.getData(this.ikey) || 0
                }
            },
            mounted() {
                this.emitter.on('process', () => {
                    this.value = this.getData(this.ikey) || 0;
                });
            }
        };
        this.props = { emitter, ikey: key, label };
    }
}

// Custom Controls for MIDI
class FileControl extends Rete.Control {
    constructor(emitter, key, label) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <input
                        type="file"
                        accept=".mid,.midi"
                        @change="change($event)"
                        @pointerdown.stop=""
                        @pointermove.stop=""
                    />
                    <div v-if="filename" class="filename">{{ filename }}</div>
                </div>
            `,
            data() {
                return {
                    filename: this.getData(this.ikey) || ""
                }
            },
            methods: {
                change(e) {
                    const file = e.target.files[0];
                    if (file) {
                        this.filename = file.name;
                        this.putData(this.ikey, file.name);
                        this.emitter.trigger('process');

                        // Upload file to backend
                        const formData = new FormData();
                        formData.append('file', file);
                        fetch('/api/upload-midi', {
                            method: 'POST',
                            body: formData
                        }).then(response => response.json())
                          .then(data => {
                              console.log('File uploaded:', data);
                              this.emitter.trigger('process');
                          });
                    }
                }
            }
        };
        this.props = { emitter, ikey: key, label };
    }
}

class RangeControl extends Rete.Control {
    constructor(emitter, key, label, min = 0, max = 100, step = 1) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label', 'min', 'max', 'step'],
            template: `
                <div class="control">
                    <label>{{ label }}: {{ value }}</label>
                    <input
                        type="range"
                        :min="min"
                        :max="max"
                        :step="step"
                        :value="value"
                        @input="change($event)"
                        @pointerdown.stop=""
                        @pointermove.stop=""
                    />
                </div>
            `,
            data() {
                return {
                    value: this.getData(this.ikey) || this.min
                }
            },
            methods: {
                change(e) {
                    this.value = parseFloat(e.target.value);
                    this.putData(this.ikey, this.value);
                    this.emitter.trigger('process');
                }
            }
        };
        this.props = { emitter, ikey: key, label, min, max, step };
    }
}

class NumberControl extends Rete.Control {
    constructor(emitter, key, label, min = -Infinity, max = Infinity, step = 1) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label', 'min', 'max', 'step'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <input
                        type="number"
                        :min="min"
                        :max="max"
                        :step="step"
                        :value="value"
                        @input="change($event)"
                        @pointerdown.stop=""
                        @pointermove.stop=""
                    />
                </div>
            `,
            data() {
                return {
                    value: this.getData(this.ikey) || 0
                }
            },
            methods: {
                change(e) {
                    this.value = parseFloat(e.target.value);
                    this.putData(this.ikey, this.value);
                    this.emitter.trigger('process');
                }
            }
        };
        this.props = { emitter, ikey: key, label, min, max, step };
    }
}

class ButtonControl extends Rete.Control {
    constructor(emitter, key, label) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label'],
            template: `
                <div class="control">
                    <button
                        @click="click()"
                        @pointerdown.stop=""
                        @pointermove.stop=""
                        class="control-button"
                    >
                        {{ label }}
                    </button>
                </div>
            `,
            methods: {
                click() {
                    this.putData(this.ikey + '_triggered', true);
                    this.emitter.trigger('process');
                }
            }
        };
        this.props = { emitter, ikey: key, label };
    }
}

class VisualizerControl extends Rete.Control {
    constructor(emitter, key, label) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label'],
            template: `
                <div class="control">
                    <label>{{ label }}</label>
                    <div class="visualizer-container">
                        <canvas ref="canvas" width="300" height="200"></canvas>
                        <div v-if="!hasData" class="no-data">No MIDI data</div>
                    </div>
                </div>
            `,
            data() {
                return {
                    hasData: false
                }
            },
            mounted() {
                this.emitter.on('process', () => {
                    this.updateVisualization();
                });
            },
            methods: {
                updateVisualization() {
                    const data = this.getData(this.ikey);
                    if (data && data.source) {
                        this.hasData = true;
                        this.renderVisualization(data);
                    } else {
                        this.hasData = false;
                    }
                },
                renderVisualization(data) {
                    const canvas = this.$refs.canvas;
                    const ctx = canvas.getContext('2d');

                    // Clear canvas
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // Simple placeholder visualization
                    ctx.fillStyle = '#007acc';
                    ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);

                    ctx.fillStyle = '#ffffff';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('MIDI Visualization', canvas.width / 2, canvas.height / 2);
                    ctx.fillText(`Type: ${data.type}`, canvas.width / 2, canvas.height / 2 + 20);
                }
            }
        };
        this.props = { emitter, ikey: key, label };
    }
}

// Piano Roll Component - FL Studio style
class PianoRollComponent extends Rete.Component {
    constructor() {
        super("Piano Roll");
    }

    builder(node) {
        const inp1 = new Rete.Input("midi", "MIDI Input", midiSocket);
        const out1 = new Rete.Output("midi", "MIDI Output", midiSocket);

        return node
            .addInput(inp1)
            .addOutput(out1)
            .addControl(new PianoRollControl(this.editor, "pianoroll", "Piano Roll Editor"))
            .addControl(new RangeControl(this.editor, "zoom", "Zoom", 0.1, 5, 0.1))
            .addControl(new RangeControl(this.editor, "scroll_x", "Scroll X", 0, 10000, 10))
            .addControl(new RangeControl(this.editor, "scroll_y", "Scroll Y", 0, 127, 1));
    }

    worker(node, inputs, outputs) {
        const midi_input = inputs["midi"] ? inputs["midi"][0] : null;
        outputs["midi"] = midi_input; // Pass through for now
    }
}

// Piano Roll Control - Interactive MIDI Editor
class PianoRollControl extends Rete.Control {
    constructor(emitter, key, label) {
        super(key);
        this.component = {
            props: ['emitter', 'ikey', 'getData', 'putData', 'label'],
            template: `
                <div class="control piano-roll-control">
                    <label>{{ label }}</label>
                    <div class="piano-roll-container">
                        <div class="piano-keys">
                            <div
                                v-for="note in 128"
                                :key="note"
                                :class="['piano-key', isBlackKey(127 - note) ? 'black' : 'white']"
                                @click="playNote(127 - note)"
                            >
                                {{ getNoteName(127 - note) }}
                            </div>
                        </div>
                        <div class="piano-grid" @click="addNote" @contextmenu="removeNote">
                            <canvas ref="pianoCanvas" width="800" height="640"></canvas>
                        </div>
                    </div>
                    <div class="piano-controls">
                        <button @click="loadMidiData">Load MIDI</button>
                        <button @click="playSequence">Play</button>
                        <button @click="stopSequence">Stop</button>
                        <button @click="clearNotes">Clear</button>
                    </div>
                </div>
            `,
            data() {
                return {
                    notes: [],
                    isPlaying: false,
                    currentTime: 0
                }
            },
            mounted() {
                this.initializePianoRoll();
                this.emitter.on('process', () => {
                    this.loadMidiData();
                });
            },
            methods: {
                initializePianoRoll() {
                    this.drawPianoRoll();
                },

                isBlackKey(note) {
                    const noteInOctave = note % 12;
                    return [1, 3, 6, 8, 10].includes(noteInOctave);
                },

                getNoteName(note) {
                    const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
                    const octave = Math.floor(note / 12) - 1;
                    return noteNames[note % 12] + octave;
                },

                async playNote(note) {
                    try {
                        await fetch('/api/midi-play-note', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ note: note, velocity: 100 })
                        });
                    } catch (error) {
                        console.error('Error playing note:', error);
                    }
                },

                async loadMidiData() {
                    try {
                        const response = await fetch('/api/midi-range?start=0&end=10000&max=1000');
                        const data = await response.json();
                        this.notes = data.notes || [];
                        this.drawPianoRoll();
                    } catch (error) {
                        console.error('Error loading MIDI data:', error);
                    }
                },

                drawPianoRoll() {
                    const canvas = this.$refs.pianoCanvas;
                    if (!canvas) return;

                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // Draw grid
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 1;

                    // Horizontal lines (notes)
                    for (let i = 0; i < 128; i++) {
                        const y = i * 5;
                        ctx.beginPath();
                        ctx.moveTo(0, y);
                        ctx.lineTo(canvas.width, y);
                        ctx.stroke();
                    }

                    // Vertical lines (time)
                    for (let i = 0; i < canvas.width; i += 20) {
                        ctx.beginPath();
                        ctx.moveTo(i, 0);
                        ctx.lineTo(i, canvas.height);
                        ctx.stroke();
                    }

                    // Draw notes
                    ctx.fillStyle = '#8b5cf6';
                    for (const note of this.notes) {
                        const x = (note.start / 1000) * 100; // Scale time to pixels
                        const y = (127 - note.note) * 5;
                        const width = Math.max(((note.end - note.start) / 1000) * 100, 5);
                        const height = 4;

                        ctx.fillRect(x, y, width, height);
                    }
                },

                addNote(event) {
                    const rect = this.$refs.pianoCanvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    const time = (x / 100) * 1000; // Convert pixels to time
                    const note = 127 - Math.floor(y / 5);

                    const newNote = {
                        start: time,
                        end: time + 500, // Default 500ms duration
                        note: note,
                        velocity: 100,
                        channel: 0
                    };

                    this.notes.push(newNote);
                    this.drawPianoRoll();
                    this.playNote(note);
                },

                removeNote(event) {
                    event.preventDefault();
                    // Implementation for removing notes
                },

                playSequence() {
                    this.isPlaying = true;
                    // Implementation for playing the sequence
                },

                stopSequence() {
                    this.isPlaying = false;
                    // Implementation for stopping playback
                },

                clearNotes() {
                    this.notes = [];
                    this.drawPianoRoll();
                }
            }
        };
        this.props = { emitter, ikey: key, label };
    }
}

// Export components
// Export optimized MIDI components only for professional use
window.NodeComponents = {
    // Professional MIDI components
    MidiFileInputComponent,
    MidiFilterComponent,
    MidiTransformComponent,
    MidiVisualizerComponent,
    MidiOutputComponent,
    PianoRollComponent,

    // Sockets
    midiSocket,
    numSocket
};
