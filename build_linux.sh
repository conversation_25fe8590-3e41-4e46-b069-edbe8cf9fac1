#!/bin/bash

echo "Building Pattern Weaver for Linux..."

# Check if required packages are installed
echo "Checking dependencies..."

# Check for CMake
if ! command -v cmake &> /dev/null; then
    echo "Error: CMake is not installed. Please install cmake:"
    echo "  Ubuntu/Debian: sudo apt-get install cmake"
    echo "  CentOS/RHEL: sudo yum install cmake"
    echo "  Arch: sudo pacman -S cmake"
    exit 1
fi

# Check for ALSA development libraries
if ! pkg-config --exists alsa; then
    echo "Error: ALSA development libraries not found. Please install:"
    echo "  Ubuntu/Debian: sudo apt-get install libasound2-dev"
    echo "  CentOS/RHEL: sudo yum install alsa-lib-devel"
    echo "  Arch: sudo pacman -S alsa-lib"
    exit 1
fi

# Check for GCC/G++
if ! command -v g++ &> /dev/null; then
    echo "Error: G++ compiler not found. Please install:"
    echo "  Ubuntu/Debian: sudo apt-get install build-essential"
    echo "  CentOS/RHEL: sudo yum groupinstall 'Development Tools'"
    echo "  Arch: sudo pacman -S base-devel"
    exit 1
fi

echo "All dependencies found!"

# Create build directory
mkdir -p build
cd build

# Configure with CMake
echo "Configuring build..."
cmake .. -DCMAKE_BUILD_TYPE=Release

if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

# Build the project
echo "Building..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

# Copy web files
echo "Copying web files..."
cp -r ../web .
cp -r ../saves .

echo ""
echo "=========================================="
echo "Pattern Weaver - Linux Build Complete!"
echo "=========================================="
echo ""
echo "To run the server:"
echo "  cd build"
echo "  ./midi_node_editor"
echo ""
echo "Then open http://localhost:8081 in your browser"
echo ""
echo "Features:"
echo "- Cross-platform compatibility (Linux/Windows)"
echo "- ALSA MIDI support for Linux"
echo "- High-performance multithreading"
echo "- Professional node-based interface"
echo "- Beautiful black and purple theme"
echo ""
