#!/bin/bash

echo "Installing dependencies for Web Node Editor..."

# Create external directory
mkdir -p external
cd external

# Download and extract Crow
echo "Downloading Crow..."
if [ ! -d "crow" ]; then
    wget https://github.com/CrowCpp/Crow/releases/download/v1.0+5/crow-v1.0+5.tar.gz
    if [ $? -ne 0 ]; then
        echo "Failed to download Crow!"
        exit 1
    fi
    
    echo "Extracting Crow..."
    tar -xzf crow-v1.0+5.tar.gz
    if [ -d "crow-v1.0+5" ]; then
        mv crow-v1.0+5 crow
    fi
    rm crow-v1.0+5.tar.gz
fi

# Download and extract nlohmann/json
echo "Downloading nlohmann/json..."
if [ ! -d "json" ]; then
    wget https://github.com/nlohmann/json/releases/download/v3.11.2/json.tar.xz
    if [ $? -ne 0 ]; then
        echo "Failed to download nlohmann/json!"
        exit 1
    fi
    
    echo "Extracting nlohmann/json..."
    tar -xf json.tar.xz
    rm json.tar.xz
fi

cd ..
echo "Dependencies installed successfully!"
echo ""
echo "You can now run ./build.sh to compile the project."
