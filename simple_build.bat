@echo off
echo Simple MIDI Node Editor Build Script
echo ====================================

REM Check if we have any C++ compiler
where cl >nul 2>&1
if %errorlevel% equ 0 (
    echo Found Visual Studio compiler (cl.exe)
    goto :build_with_msvc
)

where g++ >nul 2>&1
if %errorlevel% equ 0 (
    echo Found GCC compiler (g++.exe)
    goto :build_with_gcc
)

where clang++ >nul 2>&1
if %errorlevel% equ 0 (
    echo Found Clang compiler (clang++.exe)
    goto :build_with_clang
)

echo No C++ compiler found!
echo.
echo Please install one of the following:
echo 1. Visual Studio Community (free): https://visualstudio.microsoft.com/downloads/
echo 2. MSYS2 with GCC: https://www.msys2.org/
echo 3. TDM-GCC: https://jmeubank.github.io/tdm-gcc/
echo.
echo After installation, restart this script.
pause
exit /b 1

:build_with_msvc
echo Building with Visual Studio compiler...
REM This would require more setup for MSVC
echo MSVC build not implemented yet. Please use GCC.
pause
exit /b 1

:build_with_gcc
echo Building with GCC...
goto :build_common

:build_with_clang
echo Building with Clang...
goto :build_common

:build_common
echo.
echo Step 1: Installing dependencies...
if not exist external mkdir external
cd external

REM Download header-only libraries
echo Downloading nlohmann/json...
if not exist json.hpp (
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/nlohmann/json/releases/download/v3.11.2/json.hpp' -OutFile 'json.hpp'"
    if %errorlevel% neq 0 (
        echo Failed to download nlohmann/json
        pause
        exit /b 1
    )
)

echo Downloading Crow framework...
if not exist crow_all.h (
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/CrowCpp/Crow/releases/download/v1.0+5/crow_all.h' -OutFile 'crow_all.h'"
    if %errorlevel% neq 0 (
        echo Failed to download Crow
        pause
        exit /b 1
    )
)

cd ..

echo.
echo Step 2: Creating simplified source files...

REM Create a simplified main.cpp that doesn't require complex dependencies
echo Creating simplified_main.cpp...
(
echo #define CROW_MAIN
echo #include "external/crow_all.h"
echo #include "external/json.hpp"
echo #include ^<iostream^>
echo #include ^<vector^>
echo #include ^<string^>
echo #include ^<fstream^>
echo.
echo using json = nlohmann::json;
echo.
echo int main^(^) {
echo     crow::SimpleApp app;
echo.
echo     // Serve static HTML
echo     CROW_ROUTE^(app, "/"^)^(^[^]^(^){
echo         return crow::load_text^("web/index.html"^);
echo     }^);
echo.
echo     // Basic API endpoint
echo     CROW_ROUTE^(app, "/api/test"^)^(^[^]^(^){
echo         json response;
echo         response["status"] = "ok";
echo         response["message"] = "MIDI Node Editor is running!";
echo         return response.dump^(^);
echo     }^);
echo.
echo     std::cout ^<^< "Starting MIDI Node Editor on http://localhost:8080" ^<^< std::endl;
echo     app.port^(8080^).multithreaded^(^).run^(^);
echo     return 0;
echo }
) > simplified_main.cpp

echo.
echo Step 3: Compiling...
if not exist build mkdir build

g++ -std=c++17 -O2 -I. -pthread simplified_main.cpp -o build/midi_node_editor.exe -lws2_32 -lwsock32
if %errorlevel% neq 0 (
    echo Compilation failed!
    echo.
    echo Common issues:
    echo - Make sure you have a C++17 compatible compiler
    echo - Check that all dependencies downloaded correctly
    echo - Try installing a newer version of GCC
    pause
    exit /b 1
)

echo.
echo Step 4: Setting up web files...
if not exist build\web mkdir build\web
if not exist build\web\static mkdir build\web\static

copy web\index.html build\web\ >nul 2>&1
copy web\static\*.* build\web\static\ >nul 2>&1

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo To run the MIDI Node Editor:
echo   cd build
echo   midi_node_editor.exe
echo.
echo Then open your browser to: http://localhost:8080
echo.
pause
