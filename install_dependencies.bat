@echo off
echo Installing dependencies for Web Node Editor...

REM Create external directory
if not exist external mkdir external
cd external

REM Download and extract Crow
echo Downloading Crow...
if not exist crow (
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/CrowCpp/Crow/releases/download/v1.0+5/crow-v1.0+5.tar.gz' -OutFile 'crow.tar.gz'"
    if %errorlevel% neq 0 (
        echo Failed to download Crow!
        pause
        exit /b 1
    )
    
    echo Extracting Crow...
    powershell -Command "tar -xzf crow.tar.gz"
    if exist crow-v1.0+5 (
        ren crow-v1.0+5 crow
    )
    del crow.tar.gz
)

REM Download and extract nlohmann/json
echo Downloading nlohmann/json...
if not exist json (
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/nlohmann/json/releases/download/v3.11.2/json.tar.xz' -OutFile 'json.tar.xz'"
    if %errorlevel% neq 0 (
        echo Failed to download nlohmann/json!
        pause
        exit /b 1
    )
    
    echo Extracting nlohmann/json...
    powershell -Command "tar -xf json.tar.xz"
    del json.tar.xz
)

cd ..
echo Dependencies installed successfully!
echo.
echo You can now run build.bat to compile the project.
pause
