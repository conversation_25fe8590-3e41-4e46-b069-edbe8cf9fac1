{"name": "web-node-editor", "version": "1.0.0", "description": "A web-based visual node editor with C++ backend", "main": "web/index.html", "scripts": {"build": "cmake --build build --config Release", "start": "cd build && ./web_node_editor", "clean": "rm -rf build"}, "keywords": ["node-editor", "visual-programming", "cpp", "web", "rete"], "author": "Your Name", "license": "MIT", "devDependencies": {}, "dependencies": {}}