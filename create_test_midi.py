#!/usr/bin/env python3
"""
Create a simple test MIDI file for the MIDI Node Editor
"""

def create_test_midi():
    # Simple MIDI file with header and one track
    midi_data = bytearray()
    
    # MIDI Header
    midi_data.extend(b'MThd')  # Header chunk type
    midi_data.extend((6).to_bytes(4, 'big'))  # Header length
    midi_data.extend((0).to_bytes(2, 'big'))  # Format type 0
    midi_data.extend((1).to_bytes(2, 'big'))  # Number of tracks
    midi_data.extend((480).to_bytes(2, 'big'))  # Ticks per quarter note
    
    # Track Header
    midi_data.extend(b'MTrk')  # Track chunk type
    
    # Track data
    track_data = bytearray()
    
    # Note On events (Multiple patterns and melodies)
    notes = []

    # Create a complex melody with multiple patterns
    import random
    random.seed(42)  # For reproducible results

    # Pattern 1: Ascending scales
    for octave in range(3, 7):  # C3 to C6
        base_note = octave * 12
        scale = [0, 2, 4, 5, 7, 9, 11, 12]
        for interval in scale:
            notes.append(base_note + interval)

    # Pattern 2: Chord progressions
    chord_roots = [60, 65, 67, 72]  # C, F, G, C
    for root in chord_roots:
        # Major triad
        notes.extend([root, root + 4, root + 7])
        # Add some variations
        notes.extend([root + 12, root + 16, root + 19])

    # Pattern 3: Random melodic fragments
    for _ in range(100):
        notes.append(random.randint(48, 84))  # C3 to C6

    # Pattern 4: Bass line
    bass_notes = [36, 41, 43, 48] * 10  # Low C, F, G, C pattern
    notes.extend(bass_notes)

    print(f"Generated {len(notes)} notes for test MIDI file")
    
    for i, note in enumerate(notes):
        # Delta time (480 ticks = 1 quarter note)
        track_data.extend((0x81, 0x70))  # 480 ticks in variable length
        
        # Note On (channel 0, note, velocity 100)
        track_data.extend([0x90, note, 100])
        
        # Delta time for note off (240 ticks = half quarter note)
        track_data.extend((0x81, 0x30))  # 240 ticks
        
        # Note Off (channel 0, note, velocity 0)
        track_data.extend([0x80, note, 0])
    
    # End of track
    track_data.extend([0x00, 0xFF, 0x2F, 0x00])
    
    # Add track length
    midi_data.extend(len(track_data).to_bytes(4, 'big'))
    midi_data.extend(track_data)
    
    return midi_data

if __name__ == "__main__":
    midi_data = create_test_midi()
    
    # Write to uploads directory
    with open("build/Release/uploads/test.mid", "wb") as f:
        f.write(midi_data)
    
    print(f"Created test MIDI file with {len(midi_data)} bytes")
    print("File saved to: build/Release/uploads/test.mid")
