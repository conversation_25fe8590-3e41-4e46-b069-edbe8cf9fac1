#!/usr/bin/env python3
"""
Create a simple test MIDI file for the MIDI Node Editor
"""

def create_test_midi():
    # Simple MIDI file with header and one track
    midi_data = bytearray()
    
    # MIDI Header
    midi_data.extend(b'MThd')  # Header chunk type
    midi_data.extend((6).to_bytes(4, 'big'))  # Header length
    midi_data.extend((0).to_bytes(2, 'big'))  # Format type 0
    midi_data.extend((1).to_bytes(2, 'big'))  # Number of tracks
    midi_data.extend((480).to_bytes(2, 'big'))  # Ticks per quarter note
    
    # Track Header
    midi_data.extend(b'MTrk')  # Track chunk type
    
    # Track data
    track_data = bytearray()
    
    # Note On events (Multiple octaves and patterns)
    notes = []
    # Create multiple octaves
    for octave in range(3, 8):  # C3 to C7
        base_note = octave * 12
        # Major scale pattern
        scale = [0, 2, 4, 5, 7, 9, 11, 12]
        for interval in scale:
            notes.append(base_note + interval)

    # Add some random notes for variety
    import random
    for _ in range(50):
        notes.append(random.randint(36, 96))  # C2 to C6
    
    for i, note in enumerate(notes):
        # Delta time (480 ticks = 1 quarter note)
        track_data.extend((0x81, 0x70))  # 480 ticks in variable length
        
        # Note On (channel 0, note, velocity 100)
        track_data.extend([0x90, note, 100])
        
        # Delta time for note off (240 ticks = half quarter note)
        track_data.extend((0x81, 0x30))  # 240 ticks
        
        # Note Off (channel 0, note, velocity 0)
        track_data.extend([0x80, note, 0])
    
    # End of track
    track_data.extend([0x00, 0xFF, 0x2F, 0x00])
    
    # Add track length
    midi_data.extend(len(track_data).to_bytes(4, 'big'))
    midi_data.extend(track_data)
    
    return midi_data

if __name__ == "__main__":
    midi_data = create_test_midi()
    
    # Write to uploads directory
    with open("build/Release/uploads/test.mid", "wb") as f:
        f.write(midi_data)
    
    print(f"Created test MIDI file with {len(midi_data)} bytes")
    print("File saved to: build/Release/uploads/test.mid")
