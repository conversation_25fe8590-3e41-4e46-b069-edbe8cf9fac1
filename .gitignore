# Build directories
build/
bin/
obj/

# External dependencies
external/

# IDE files
.vs/
.vscode/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates

# Compiled binaries
*.exe
*.dll
*.so
*.dylib
*.a
*.lib

# CMake files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake

# Temporary files
*.tmp
*.temp
*.log
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Node.js (if using npm for frontend dependencies)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Saved graphs (optional - you might want to keep these)
# saves/*.json
