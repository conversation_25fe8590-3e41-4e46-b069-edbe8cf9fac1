# Quick Start Guide

## 1. Build the Project

### Windows
```bash
# Double-click or run from command prompt
build.bat
```

### Linux/macOS
```bash
chmod +x build.sh
./build.sh
```

## 2. Run the Application

### Windows
```bash
# Option 1: Use test script
test_build.bat

# Option 2: Manual
cd build\Release
web_node_editor.exe
```

### Linux/macOS
```bash
cd build
./web_node_editor
```

## 3. Open in Browser

Navigate to: `http://localhost:8080`

## 4. Create Your First Node Graph

1. **Add an Input Node**: Click "Add Input" button
2. **Add a Process Node**: Click "Add Process" button  
3. **Add an Output Node**: Click "Add Output" button
4. **Connect Nodes**: Drag from output socket (right) to input socket (left)
5. **Configure Nodes**:
   - Input: Enter a number value
   - Process: Select operation (add, subtract, multiply, divide)
   - Output: View the result

## 5. Save Your Work

1. Click "Save" button
2. Enter a filename (e.g., "my_graph.json")
3. Click "Save"

## 6. Load Saved Graphs

1. Click "Load" button
2. Enter the filename
3. Click "Load"

## Example Graph

Try loading the example graph:
1. Click "Load"
2. Enter "example.json"
3. Click "Load"

This will load a simple addition graph: 10 + 5 = 15

## Troubleshooting

- **Port 8080 in use**: Change the port in `main.cpp` line 339
- **Build fails**: Ensure you have CMake 3.15+ and a C++20 compiler
- **Nodes don't appear**: Refresh the browser page
- **Can't connect nodes**: Make sure to drag from output (right) to input (left)

## Next Steps

- Explore the code in `web/static/` to understand the frontend
- Modify `main.cpp` to add new API endpoints
- Create custom node types in `components.js`
- Add new mathematical operations or data processing features
