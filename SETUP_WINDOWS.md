# Windows Setup Guide

## Option 1: Install Visual Studio (Recommended)

1. **Download Visual Studio Community** (free):
   - Go to: https://visualstudio.microsoft.com/downloads/
   - Download "Visual Studio Community 2022"

2. **During installation, select**:
   - "Desktop development with C++"
   - Make sure "CMake tools for C++" is checked

3. **Install CMake** (if not included):
   - Download from: https://cmake.org/download/
   - Choose "Windows x64 Installer"
   - During installation, select "Add CMake to system PATH"

4. **Build the project**:
   ```cmd
   build.bat
   ```

## Option 2: Use MinGW-w64 (Lighter alternative)

1. **Install MSYS2**:
   - Download from: https://www.msys2.org/
   - Follow installation instructions
   - Open MSYS2 terminal and run:
   ```bash
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-cmake
   pacman -S make
   ```

2. **Add to PATH**:
   - Add `C:\msys64\mingw64\bin` to your Windows PATH
   - Restart command prompt

3. **Build the project**:
   ```cmd
   build_mingw.bat
   ```

## Option 3: Direct Compilation (No CMake)

1. **Install MinGW-w64**:
   - Download TDM-GCC from: https://jmeubank.github.io/tdm-gcc/
   - Or use MSYS2 as above

2. **Build directly**:
   ```cmd
   build_direct.bat
   ```

## Option 4: Use Chocolatey (Package Manager)

1. **Install Chocolatey**:
   - Open PowerShell as Administrator
   - Run: 
   ```powershell
   Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
   ```

2. **Install tools**:
   ```cmd
   choco install cmake
   choco install mingw
   ```

3. **Build**:
   ```cmd
   build_mingw.bat
   ```

## Troubleshooting

### "Visual Studio not found"
- Install Visual Studio Community with C++ support
- Or use MinGW alternative

### "CMake not found"
- Download from cmake.org
- Make sure it's added to PATH
- Restart command prompt

### "gcc not found"
- Install MinGW-w64
- Add to PATH: `C:\msys64\mingw64\bin` or similar
- Restart command prompt

### "Dependencies not found"
- Run `install_dependencies.bat` first
- Check internet connection
- Try manual download if automatic fails

### Build errors
- Make sure you have C++20 support
- Try different build scripts in order:
  1. `build.bat` (tries multiple generators)
  2. `build_mingw.bat` (MinGW specific)
  3. `build_direct.bat` (direct compilation)

## Verification

After successful build:
1. Navigate to `build` folder
2. Run `web_node_editor.exe`
3. Open browser to `http://localhost:8080`
4. You should see the node editor interface

## Quick Test Commands

```cmd
# Check if tools are installed
cmake --version
gcc --version
g++ --version

# Check PATH
echo %PATH%
```

## Alternative: Use Online IDE

If local setup is problematic, try:
- **Replit**: https://replit.com/ (supports C++)
- **CodeSpaces**: GitHub Codespaces
- **Gitpod**: https://gitpod.io/

Upload the project files and build there.
