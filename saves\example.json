{"nodes": [{"id": 1, "type": "input", "name": "Input", "x": 100, "y": 100, "data": {"value": "10"}, "inputs": [], "outputs": [1]}, {"id": 2, "type": "input", "name": "Input", "x": 100, "y": 250, "data": {"value": "5"}, "inputs": [], "outputs": [2]}, {"id": 3, "type": "process", "name": "Process", "x": 350, "y": 175, "data": {"operation": "add"}, "inputs": [1, 2], "outputs": [3]}, {"id": 4, "type": "output", "name": "Output", "x": 600, "y": 175, "data": {"display": 15}, "inputs": [3], "outputs": []}], "connections": [{"id": 1, "sourceNodeId": 1, "sourcePortId": 0, "targetNodeId": 3, "targetPortId": 0}, {"id": 2, "sourceNodeId": 2, "sourcePortId": 0, "targetNodeId": 3, "targetPortId": 1}, {"id": 3, "sourceNodeId": 3, "sourcePortId": 0, "targetNodeId": 4, "targetPortId": 0}]}