#include "midi_parser.h"
#include <chrono>
#include <algorithm>
#include <execution>
#include <cstring>
#include <iostream>

// FastMidiParser implementation
uint32_t FastMidiParser::read_variable_length() {
    uint32_t value = 0;
    uint8_t byte;
    
    do {
        if (position >= file_data.size()) return 0;
        byte = file_data[position++];
        value = (value << 7) | (byte & 0x7F);
    } while (byte & 0x80);
    
    return value;
}

uint32_t FastMidiParser::read_uint32() {
    if (position + 4 > file_data.size()) return 0;
    uint32_t value = (file_data[position] << 24) |
                     (file_data[position + 1] << 16) |
                     (file_data[position + 2] << 8) |
                     file_data[position + 3];
    position += 4;
    return value;
}

uint16_t FastMidiParser::read_uint16() {
    if (position + 2 > file_data.size()) return 0;
    uint16_t value = (file_data[position] << 8) | file_data[position + 1];
    position += 2;
    return value;
}

uint8_t FastMidiParser::read_uint8() {
    if (position >= file_data.size()) return 0;
    return file_data[position++];
}

void FastMidiParser::skip_bytes(size_t count) {
    position = std::min(position + count, file_data.size());
}

FastMidiParser::ParseResult FastMidiParser::parse_file(const std::string& filename) {
    auto start_time = std::chrono::high_resolution_clock::now();
    ParseResult result;
    
    // Load entire file into memory for fastest parsing
    std::ifstream file(filename, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
        result.error_message = "Cannot open file: " + filename;
        return result;
    }
    
    size_t file_size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    file_data.resize(file_size);
    file.read(reinterpret_cast<char*>(file_data.data()), file_size);
    file.close();
    
    result = parse_data(file_data);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.parse_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    return result;
}

FastMidiParser::ParseResult FastMidiParser::parse_data(const std::vector<uint8_t>& data) {
    ParseResult result;
    file_data = data;
    position = 0;
    
    // Parse header
    if (file_data.size() < 14) {
        result.error_message = "File too small to be valid MIDI";
        return result;
    }
    
    // Check MIDI header
    if (std::memcmp(file_data.data(), "MThd", 4) != 0) {
        result.error_message = "Invalid MIDI header";
        return result;
    }
    
    position = 4;
    uint32_t header_length = read_uint32();
    if (header_length != 6) {
        result.error_message = "Invalid header length";
        return result;
    }
    
    result.header.format = read_uint16();
    result.header.track_count = read_uint16();
    result.header.ticks_per_quarter = read_uint16();
    
    // Reserve memory for performance
    result.notes.reserve(1000000); // Reserve for 1M notes initially
    result.events.reserve(100000);
    
    // Parse tracks
    for (uint16_t track = 0; track < result.header.track_count; ++track) {
        if (position + 8 > file_data.size()) break;
        
        // Check track header
        if (std::memcmp(file_data.data() + position, "MTrk", 4) != 0) {
            result.error_message = "Invalid track header";
            return result;
        }
        
        position += 4;
        uint32_t track_length = read_uint32();
        size_t track_end = position + track_length;
        
        uint32_t current_tick = 0;
        uint8_t running_status = 0;
        
        // Track active notes for note-off events
        struct ActiveNote {
            uint32_t start_tick;
            uint8_t channel;
            uint8_t note;
            uint8_t velocity;
        };
        std::vector<ActiveNote> active_notes;
        active_notes.reserve(128); // Max polyphony per track
        
        while (position < track_end && position < file_data.size()) {
            // Read delta time
            uint32_t delta_time = read_variable_length();
            current_tick += delta_time;
            
            if (position >= file_data.size()) break;
            
            uint8_t status = read_uint8();
            
            // Handle running status
            if (status < 0x80) {
                position--; // Put byte back
                status = running_status;
            } else {
                running_status = status;
            }
            
            uint8_t event_type = status & 0xF0;
            uint8_t channel = status & 0x0F;
            
            switch (event_type) {
                case 0x90: { // Note On
                    uint8_t note = read_uint8();
                    uint8_t velocity = read_uint8();
                    
                    if (velocity > 0) {
                        active_notes.push_back({current_tick, channel, note, velocity});
                    } else {
                        // Velocity 0 = Note Off
                        auto it = std::find_if(active_notes.begin(), active_notes.end(),
                            [channel, note](const ActiveNote& an) {
                                return an.channel == channel && an.note == note;
                            });
                        
                        if (it != active_notes.end()) {
                            MidiNote midi_note;
                            midi_note.start_tick = it->start_tick;
                            midi_note.end_tick = current_tick;
                            midi_note.channel = channel;
                            midi_note.note = note;
                            midi_note.velocity = it->velocity;
                            midi_note.track_id = track;
                            
                            result.notes.push_back(midi_note);
                            active_notes.erase(it);
                        }
                    }
                    break;
                }
                
                case 0x80: { // Note Off
                    uint8_t note = read_uint8();
                    uint8_t velocity = read_uint8();
                    
                    auto it = std::find_if(active_notes.begin(), active_notes.end(),
                        [channel, note](const ActiveNote& an) {
                            return an.channel == channel && an.note == note;
                        });
                    
                    if (it != active_notes.end()) {
                        MidiNote midi_note;
                        midi_note.start_tick = it->start_tick;
                        midi_note.end_tick = current_tick;
                        midi_note.channel = channel;
                        midi_note.note = note;
                        midi_note.velocity = it->velocity;
                        midi_note.track_id = track;
                        
                        result.notes.push_back(midi_note);
                        active_notes.erase(it);
                    }
                    break;
                }
                
                case 0xA0: // Polyphonic Key Pressure
                case 0xB0: // Control Change
                case 0xE0: // Pitch Bend
                    skip_bytes(2);
                    break;
                    
                case 0xC0: // Program Change
                case 0xD0: // Channel Pressure
                    skip_bytes(1);
                    break;
                    
                case 0xF0: { // System Exclusive or Meta Event
                    if (status == 0xFF) { // Meta Event
                        uint8_t meta_type = read_uint8();
                        uint32_t length = read_variable_length();
                        
                        if (meta_type == 0x2F) { // End of Track
                            break;
                        }
                        
                        skip_bytes(length);
                    } else {
                        // System Exclusive
                        uint32_t length = read_variable_length();
                        skip_bytes(length);
                    }
                    break;
                }
                
                default:
                    // Unknown event, try to skip
                    if (position < file_data.size()) {
                        position++;
                    }
                    break;
            }
        }
        
        // Handle any remaining active notes (extend to end of track)
        for (const auto& active : active_notes) {
            MidiNote midi_note;
            midi_note.start_tick = active.start_tick;
            midi_note.end_tick = current_tick;
            midi_note.channel = active.channel;
            midi_note.note = active.note;
            midi_note.velocity = active.velocity;
            midi_note.track_id = track;
            
            result.notes.push_back(midi_note);
        }
    }
    
    // Sort notes by start time for efficient processing
    std::sort(std::execution::par_unseq, result.notes.begin(), result.notes.end());
    
    result.success = true;
    return result;
}

bool FastMidiParser::export_to_fast_format(const std::string& input_midi, const std::string& output_file) {
    auto parse_result = parse_file(input_midi);
    if (!parse_result.success) {
        return false;
    }
    
    return FastMidiFormat::save(output_file, parse_result.header, parse_result.notes, parse_result.events);
}

FastMidiParser::ParseResult FastMidiParser::load_from_fast_format(const std::string& filename) {
    ParseResult result;
    
    if (FastMidiFormat::load(filename, result.header, result.notes, result.events)) {
        result.success = true;
    } else {
        result.error_message = "Failed to load fast format file";
    }
    
    return result;
}

// MidiUtils implementation
double MidiUtils::ticks_to_seconds(uint32_t ticks, uint16_t ticks_per_quarter, uint32_t tempo_bpm) {
    double seconds_per_quarter = 60.0 / tempo_bpm;
    return (static_cast<double>(ticks) / ticks_per_quarter) * seconds_per_quarter;
}

uint32_t MidiUtils::seconds_to_ticks(double seconds, uint16_t ticks_per_quarter, uint32_t tempo_bpm) {
    double seconds_per_quarter = 60.0 / tempo_bpm;
    return static_cast<uint32_t>((seconds / seconds_per_quarter) * ticks_per_quarter);
}

std::string MidiUtils::note_to_name(uint8_t note) {
    const char* note_names[] = {"C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"};
    int octave = (note / 12) - 1;
    int note_index = note % 12;
    return std::string(note_names[note_index]) + std::to_string(octave);
}

MidiUtils::MidiStats MidiUtils::analyze(const std::vector<MidiNote>& notes, uint16_t ticks_per_quarter) {
    MidiStats stats;
    stats.total_notes = notes.size();
    
    if (notes.empty()) return stats;
    
    stats.min_note = 127;
    stats.max_note = 0;
    stats.total_duration_ticks = 0;
    stats.notes_per_channel.resize(16, 0);
    
    uint32_t max_end_tick = 0;
    
    for (const auto& note : notes) {
        stats.min_note = std::min(stats.min_note, note.note);
        stats.max_note = std::max(stats.max_note, note.note);
        max_end_tick = std::max(max_end_tick, note.end_tick);
        
        if (note.channel < 16) {
            stats.notes_per_channel[note.channel]++;
        }
    }
    
    stats.note_range = stats.max_note - stats.min_note;
    stats.total_duration_ticks = max_end_tick;
    stats.total_duration_seconds = ticks_to_seconds(max_end_tick, ticks_per_quarter);
    
    return stats;
}
