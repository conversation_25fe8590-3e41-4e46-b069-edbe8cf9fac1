#!/bin/bash

echo "Installing Pattern Weaver dependencies for Linux..."

# Detect Linux distribution
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
elif type lsb_release >/dev/null 2>&1; then
    OS=$(lsb_release -si)
    VER=$(lsb_release -sr)
elif [ -f /etc/lsb-release ]; then
    . /etc/lsb-release
    OS=$DISTRIB_ID
    VER=$DISTRIB_RELEASE
elif [ -f /etc/debian_version ]; then
    OS=Debian
    VER=$(cat /etc/debian_version)
elif [ -f /etc/SuSe-release ]; then
    OS=openSUSE
elif [ -f /etc/redhat-release ]; then
    OS=RedHat
else
    OS=$(uname -s)
    VER=$(uname -r)
fi

echo "Detected OS: $OS $VER"

# Install dependencies based on distribution
case $OS in
    "Ubuntu"|"Debian GNU/Linux"|"Debian")
        echo "Installing dependencies for Ubuntu/Debian..."
        sudo apt-get update
        sudo apt-get install -y \
            build-essential \
            cmake \
            libasound2-dev \
            pkg-config \
            git \
            wget \
            curl
        ;;
    "CentOS Linux"|"Red Hat Enterprise Linux"|"RedHat"|"Fedora")
        echo "Installing dependencies for CentOS/RHEL/Fedora..."
        if command -v dnf &> /dev/null; then
            sudo dnf groupinstall -y "Development Tools"
            sudo dnf install -y \
                cmake \
                alsa-lib-devel \
                pkgconfig \
                git \
                wget \
                curl
        else
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y \
                cmake \
                alsa-lib-devel \
                pkgconfig \
                git \
                wget \
                curl
        fi
        ;;
    "Arch Linux"|"Manjaro Linux")
        echo "Installing dependencies for Arch Linux..."
        sudo pacman -Sy --needed \
            base-devel \
            cmake \
            alsa-lib \
            pkgconf \
            git \
            wget \
            curl
        ;;
    "openSUSE"*)
        echo "Installing dependencies for openSUSE..."
        sudo zypper install -y \
            gcc-c++ \
            cmake \
            alsa-devel \
            pkg-config \
            git \
            wget \
            curl
        ;;
    *)
        echo "Unknown distribution: $OS"
        echo "Please manually install the following packages:"
        echo "- build-essential or equivalent (gcc, g++, make)"
        echo "- cmake"
        echo "- ALSA development libraries (libasound2-dev or alsa-lib-devel)"
        echo "- pkg-config"
        echo "- git"
        exit 1
        ;;
esac

echo ""
echo "Dependencies installed successfully!"
echo ""
echo "Next steps:"
echo "1. Run ./build_linux.sh to build Pattern Weaver"
echo "2. Or use CMake manually:"
echo "   mkdir build && cd build"
echo "   cmake .. -DCMAKE_BUILD_TYPE=Release"
echo "   make -j\$(nproc)"
echo ""
