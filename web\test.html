<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-box {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Pattern Weaver Test Page</h1>
    <div class="test-box">
        <h2>CSS Test</h2>
        <p>If you can see this styled box, CSS is working!</p>
    </div>
    
    <div id="vue-test">
        <h2>Vue.js Test</h2>
        <p>Message: {{ message }}</p>
        <button @click="changeMessage">Click me!</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script>
        console.log('Test page loaded');
        console.log('Vue available:', typeof Vue !== 'undefined');
        
        new Vue({
            el: '#vue-test',
            data: {
                message: 'Vue.js is working!'
            },
            methods: {
                changeMessage() {
                    this.message = 'Button clicked! Vue.js is definitely working!';
                }
            },
            mounted() {
                console.log('Vue test app mounted');
            }
        });
    </script>
</body>
</html>
