@echo off
echo Building Web Node Editor directly with GCC...

REM Check if GCC is available
where g++ >nul 2>&1
if %errorlevel% neq 0 (
    echo G++ not found in PATH!
    echo Please install MinGW-w64 or another C++ compiler.
    pause
    exit /b 1
)

REM Install dependencies first
if not exist external (
    echo Installing dependencies...
    call install_dependencies.bat
    if %errorlevel% neq 0 (
        echo Failed to install dependencies!
        pause
        exit /b 1
    )
)

REM Create directories
if not exist build mkdir build
if not exist build\web mkdir build\web
if not exist build\saves mkdir build\saves

REM Copy web files
xcopy /E /I /Y web build\web >nul
xcopy /E /I /Y saves build\saves >nul

REM Compile
echo Compiling...
g++ -std=c++20 ^
    -I external/crow/include ^
    -I external/json/include ^
    -O2 ^
    -DCROW_ENABLE_SSL=0 ^
    -pthread ^
    main.cpp ^
    -o build/web_node_editor.exe ^
    -lws2_32 ^
    -lwsock32

if %errorlevel% neq 0 (
    echo Compilation failed!
    echo.
    echo Common issues:
    echo - Make sure you have a C++20 compatible compiler
    echo - Check that dependencies are installed in external/ folder
    echo - Try installing a newer version of MinGW-w64
    pause
    exit /b 1
)

echo Build completed successfully!
echo.
echo To run the application:
echo   cd build
echo   web_node_editor.exe
echo.
echo Then open your browser to http://localhost:8080
pause
