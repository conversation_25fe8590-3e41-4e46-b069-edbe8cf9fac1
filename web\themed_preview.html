<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MIDI Node Editor - New Theme Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e0e6ed;
            height: 100vh;
            overflow: hidden;
        }

        #app {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .toolbar {
            background: linear-gradient(90deg, #1a1a2e 0%, #16213e 100%);
            padding: 12px 24px;
            border-bottom: 1px solid rgba(139, 92, 246, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
        }

        .toolbar h1 {
            font-size: 1.4em;
            font-weight: 600;
            background: linear-gradient(135deg, #a855f7 0%, #3b82f6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .toolbar-buttons {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .button-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .button-group label {
            font-size: 11px;
            color: #9ca3af;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .button-group > div {
            display: flex;
            gap: 6px;
        }

        .toolbar-buttons button {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: #ffffff;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
            border: 1px solid rgba(139, 92, 246, 0.2);
        }

        .toolbar-buttons button:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
            transform: translateY(-1px);
        }

        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .sidebar {
            width: 320px;
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 1px solid rgba(139, 92, 246, 0.15);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header {
            padding: 16px 24px;
            background: linear-gradient(90deg, #16213e 0%, #1a1a2e 100%);
            border-bottom: 1px solid rgba(139, 92, 246, 0.2);
            font-weight: 600;
            font-size: 14px;
            color: #e0e6ed;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .node-category {
            margin-bottom: 24px;
        }

        .node-category h4 {
            color: #cbd5e1;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
            padding-left: 8px;
        }

        .node-list {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .node-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.3) 100%);
            border: 1px solid rgba(139, 92, 246, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            font-weight: 500;
            color: #cbd5e1;
        }

        .node-item:hover {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(168, 85, 247, 0.1) 100%);
            border-color: rgba(139, 92, 246, 0.3);
            transform: translateX(4px);
            color: #e0e6ed;
        }

        .node-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        #editor {
            flex: 1;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .demo-nodes {
            display: flex;
            gap: 40px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        /* Sample Node Styling */
        .demo-node {
            background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 12px;
            padding: 0 0 12px 0;
            min-width: 180px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(139, 92, 246, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .demo-node:hover {
            border-color: rgba(139, 92, 246, 0.6);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(139, 92, 246, 0.3);
            transform: translateY(-2px);
        }

        .demo-node .title {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: #ffffff;
            padding: 12px 16px;
            margin: 0;
            border-radius: 12px 12px 0 0;
            font-weight: 600;
            text-align: center;
            font-size: 13px;
            letter-spacing: 0.3px;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
        }

        .demo-node.input .title {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        }

        .demo-node.process .title {
            background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
        }

        .demo-node.output .title {
            background: linear-gradient(135deg, #ec4899 0%, #f97316 100%);
        }

        .demo-node .content {
            padding: 16px;
            color: #cbd5e1;
            font-size: 12px;
            text-align: center;
        }

        .demo-socket {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 2px solid rgba(139, 92, 246, 0.6);
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            margin: 8px auto;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .preview-info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 12px;
            padding: 16px;
            color: #cbd5e1;
            font-size: 12px;
            max-width: 250px;
            backdrop-filter: blur(10px);
        }

        .preview-info h3 {
            color: #a855f7;
            margin-bottom: 8px;
            font-size: 14px;
        }

        /* Scrollbars */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(15, 15, 35, 0.3);
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-radius: 5px;
            border: 2px solid rgba(15, 15, 35, 0.3);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="toolbar">
            <h1>MIDI Node Editor</h1>
            <div class="toolbar-buttons">
                <div class="button-group">
                    <label>MIDI Nodes:</label>
                    <div>
                        <button>MIDI Input</button>
                        <button>Filter</button>
                        <button>Transform</button>
                        <button>Visualizer</button>
                        <button>MIDI Output</button>
                    </div>
                </div>
                <div class="button-group">
                    <label>Basic Nodes:</label>
                    <div>
                        <button>Input</button>
                        <button>Process</button>
                        <button>Output</button>
                    </div>
                </div>
                <div class="button-group">
                    <label>Actions:</label>
                    <div>
                        <button>Clear</button>
                        <button>Save</button>
                        <button>Load</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-content">
            <div class="sidebar">
                <div class="sidebar-header">Node Library</div>
                <div class="sidebar-content">
                    <div class="node-category">
                        <h4>MIDI Processing</h4>
                        <div class="node-list">
                            <div class="node-item">
                                <span class="node-icon">🎵</span>
                                <span>MIDI Input</span>
                            </div>
                            <div class="node-item">
                                <span class="node-icon">🔍</span>
                                <span>Filter</span>
                            </div>
                            <div class="node-item">
                                <span class="node-icon">⚡</span>
                                <span>Transform</span>
                            </div>
                            <div class="node-item">
                                <span class="node-icon">📊</span>
                                <span>Visualizer</span>
                            </div>
                            <div class="node-item">
                                <span class="node-icon">🎹</span>
                                <span>MIDI Output</span>
                            </div>
                        </div>
                    </div>
                    <div class="node-category">
                        <h4>Basic Nodes</h4>
                        <div class="node-list">
                            <div class="node-item">
                                <span class="node-icon">📥</span>
                                <span>Input</span>
                            </div>
                            <div class="node-item">
                                <span class="node-icon">⚙️</span>
                                <span>Process</span>
                            </div>
                            <div class="node-item">
                                <span class="node-icon">📤</span>
                                <span>Output</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="editor">
                <div class="demo-nodes">
                    <div class="demo-node input">
                        <div class="title">MIDI Input</div>
                        <div class="content">
                            Load MIDI files
                            <div class="demo-socket"></div>
                        </div>
                    </div>
                    <div class="demo-node process">
                        <div class="title">Filter</div>
                        <div class="content">
                            <div class="demo-socket"></div>
                            Process notes
                            <div class="demo-socket"></div>
                        </div>
                    </div>
                    <div class="demo-node output">
                        <div class="title">MIDI Output</div>
                        <div class="content">
                            <div class="demo-socket"></div>
                            Export results
                        </div>
                    </div>
                </div>
                <div class="preview-info">
                    <h3>🎨 New Theme Preview</h3>
                    <p>This is the new black and purple dark mode theme inspired by professional node-based editors.</p>
                    <br>
                    <p><strong>Features:</strong></p>
                    <ul style="margin-left: 16px; margin-top: 8px;">
                        <li>Elegant gradients</li>
                        <li>Smooth animations</li>
                        <li>Professional styling</li>
                        <li>Better contrast</li>
                        <li>Modern typography</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
