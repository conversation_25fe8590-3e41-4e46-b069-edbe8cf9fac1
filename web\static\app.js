// Main Application
class NodeEditor {
    constructor() {
        this.editor = null;
        this.engine = null;
        this.components = {};
        this.init();
    }

    async init() {
        console.log('Initializing NodeEditor...');
        const container = document.querySelector('#editor');

        // Create editor and engine
        this.editor = new Rete.NodeEditor('demo@0.1.0', container);
        this.engine = new Rete.Engine('demo@0.1.0');

        console.log('Editor and engine created');
        
        // Check if NodeComponents is available
        if (typeof NodeComponents === 'undefined') {
            console.error('NodeComponents is not defined! Check components.js loading.');
            return;
        }

        console.log('Available NodeComponents:', Object.keys(NodeComponents));

        // Register MIDI components only (removed unused basic components)
        this.components.midiInput = new NodeComponents.MidiFileInputComponent();
        this.components.midiFilter = new NodeComponents.MidiFilterComponent();
        this.components.midiTransform = new NodeComponents.MidiTransformComponent();
        this.components.midiVisualizer = new NodeComponents.MidiVisualizerComponent();
        this.components.midiOutput = new NodeComponents.MidiOutputComponent();
        this.components.pianoRoll = new NodeComponents.PianoRollComponent();
        this.components.midiLoad = new NodeComponents.MidiLoadComponent();

        // Register all components
        for (const component of Object.values(this.components)) {
            await this.editor.register(component);
            await this.engine.register(component);
        }

        console.log('All components registered successfully');
        
        // Use plugins
        this.editor.use(ConnectionPlugin.default);
        // Temporarily disable context menu to avoid filters.js error
        // this.editor.use(ContextMenuPlugin.default, {
        //     'MIDI File Input': () => this.addNode('MIDI File Input', 'midiInput'),
        //     'Piano Roll': () => this.addNode('Piano Roll', 'pianoRoll'),
        //     'MIDI Filter': () => this.addNode('MIDI Filter', 'midiFilter'),
        //     'MIDI Transform': () => this.addNode('MIDI Transform', 'midiTransform'),
        //     'MIDI Visualizer': () => this.addNode('MIDI Visualizer', 'midiVisualizer'),
        //     'MIDI Output': () => this.addNode('MIDI Output', 'midiOutput'),
        //     '---': null,
        //     'Add Input': () => this.addNode('Input', 'input'),
        //     'Add Process': () => this.addNode('Process', 'process'),
        //     'Add Output': () => this.addNode('Output', 'output'),
        //     '---2': null,
        //     'Delete': () => this.deleteSelected()
        // });
        this.editor.use(AreaPlugin);

        // Windows-style node rendering with header bar and control buttons
        this.editor.on('rendernode', ({ el, node }) => {
            // Use CSS classes instead of inline styles for better performance
            el.className = 'windows-node';

            // Store minimized state in node data if not already set
            if (node.data.minimized === undefined) {
                node.data.minimized = false;
            }

            // Store maximized state in node data if not already set
            if (node.data.maximized === undefined) {
                node.data.maximized = false;
            }

            // Add minimized class if node is minimized
            if (node.data.minimized) {
                el.classList.add('minimized');
            }

            // Add maximized class if node is maximized
            if (node.data.maximized) {
                el.classList.add('maximized');
            }

            // Wait for Vue.js components to render, then add Windows-style header
            setTimeout(() => {
                this.addWindowsStyleHeader(el, node);
            }, 100);
        });

        // Separate method to add Windows-style header after Vue.js rendering
        this.addWindowsStyleHeader = (el, node) => {
            // Check if this node already has a Windows-style header
            if (el.querySelector('.node-header')) {
                return; // Already processed
            }

            // Store the original content (now includes Vue.js rendered content)
            const originalContent = el.innerHTML;

            // Performance optimization: use DocumentFragment for batch DOM operations
            const fragment = document.createDocumentFragment();

            // Create Windows-style header bar
            const headerBar = document.createElement('div');
            headerBar.className = 'node-header';

            // Add node title to header
            const title = document.createElement('div');
            title.className = 'node-title';
            title.textContent = node.name;
            headerBar.appendChild(title);

            // Create control buttons container
            const controlButtons = document.createElement('div');
            controlButtons.className = 'node-control-buttons';

            // Create minimize button
            const minimizeBtn = document.createElement('button');
            minimizeBtn.className = 'node-btn';
            minimizeBtn.textContent = '−';
            minimizeBtn.title = 'Minimize';
            minimizeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleNodeMinimize(node, el);
            });
            controlButtons.appendChild(minimizeBtn);

            // Create maximize button
            const maximizeBtn = document.createElement('button');
            maximizeBtn.className = 'node-btn';
            maximizeBtn.textContent = '□';
            maximizeBtn.title = 'Maximize';
            maximizeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleNodeMaximize(node, el);
            });
            controlButtons.appendChild(maximizeBtn);

            // Create close button
            const closeBtn = document.createElement('button');
            closeBtn.className = 'node-btn close-btn';
            closeBtn.textContent = '×';
            closeBtn.title = 'Close';
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.removeNode(node);
            });
            controlButtons.appendChild(closeBtn);

            headerBar.appendChild(controlButtons);
            fragment.appendChild(headerBar);

            // Create content container and preserve original content
            const contentContainer = document.createElement('div');
            contentContainer.className = 'node-content';
            contentContainer.innerHTML = originalContent;

            // Apply minimize state
            if (node.data.minimized) {
                contentContainer.style.display = 'none';
            }

            fragment.appendChild(contentContainer);

            // Single DOM update for better performance
            el.innerHTML = '';
            el.appendChild(fragment);
        };

        // Event listeners
        this.setupEventListeners();
        
        // Load initial graph
        await this.loadGraphFromServer();
        
        // Trigger initial processing
        this.editor.view.resize();
        this.editor.trigger('process');
    }

    setupEventListeners() {
        // Node events
        this.editor.on('nodecreated', async (node) => {
            await this.syncNodeToServer(node, 'create');
        });

        this.editor.on('noderemoved', async (node) => {
            await this.syncNodeToServer(node, 'delete');
        });

        this.editor.on('connectioncreated', async (connection) => {
            await this.syncConnectionToServer(connection, 'create');
        });

        this.editor.on('connectionremoved', async (connection) => {
            await this.syncConnectionToServer(connection, 'delete');
        });

        // Process events
        this.editor.on('process nodecreated noderemoved connectioncreated connectionremoved', async () => {
            await this.engine.process(this.editor.toJSON());
        });
    }

    async addNode(type, componentKey) {
        console.log(`Adding node: ${type} with component: ${componentKey}`);

        const component = this.components[componentKey];
        if (!component) {
            console.error(`Component ${componentKey} not found!`);
            console.log('Available components:', Object.keys(this.components));
            return;
        }

        // Create a new node using the component
        const node = new Rete.Node(type);
        await component.builder(node);

        // Position node at center of view
        node.position = [200, 200]; // Simple default position
        node.data.type = type.toLowerCase().replace(' ', '_');

        this.editor.addNode(node);
        return node;
    }

    deleteSelected() {
        const selected = this.editor.selected.list;
        selected.forEach(item => {
            if (item instanceof Rete.Node) {
                this.editor.removeNode(item);
            } else if (item instanceof Rete.Connection) {
                this.editor.removeConnection(item);
            }
        });
    }
    
    // Windows-style node control methods
    toggleNodeMinimize(node) {
        node.data.minimized = !node.data.minimized;
        
        // If node is maximized and we're minimizing it, unmaximize it
        if (node.data.maximized && node.data.minimized) {
            node.data.maximized = false;
        }
        
        this.editor.view.updateNode(node);
    }
    
    toggleNodeMaximize(node) {
        node.data.maximized = !node.data.maximized;
        
        // If node is minimized and we're maximizing it, unminimize it
        if (node.data.minimized && node.data.maximized) {
            node.data.minimized = false;
        }
        
        // Save original position if maximizing
        if (node.data.maximized && !node.data.originalPosition) {
            node.data.originalPosition = [...node.position];
            node.data.originalWidth = node.width;
            node.data.originalHeight = node.height;
            
            // Center the node in the editor
            const editorEl = document.getElementById('editor');
            const editorRect = editorEl.getBoundingClientRect();
            const zoom = this.editor.view.area.transform.k;
            
            // Calculate center position accounting for zoom and pan
            const centerX = (editorRect.width / 2 - this.editor.view.area.transform.x) / zoom;
            const centerY = (editorRect.height / 2 - this.editor.view.area.transform.y) / zoom;
            
            node.position = [centerX - 400, centerY - 300]; // Center the node
        } 
        // Restore original position if unmaximizing
        else if (!node.data.maximized && node.data.originalPosition) {
            node.position = [...node.data.originalPosition];
            delete node.data.originalPosition;
            delete node.data.originalWidth;
            delete node.data.originalHeight;
        }
        
        this.editor.view.updateNode(node);
    }
    
    removeNode(node) {
        if (confirm(`Are you sure you want to remove the "${node.name}" node?`)) {
            this.editor.removeNode(node);
        }
    }

    async syncNodeToServer(node, action) {
        try {
            const nodeData = {
                id: node.id,
                type: node.data.type || node.name.toLowerCase(),
                name: node.name,
                x: node.position[0],
                y: node.position[1],
                data: node.data
            };

            if (action === 'create') {
                const response = await fetch('/api/nodes', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(nodeData)
                });
                const result = await response.json();
                node.id = result.id;
            } else if (action === 'delete') {
                await fetch(`/api/nodes/${node.id}`, {
                    method: 'DELETE'
                });
            }
        } catch (error) {
            console.error('Error syncing node:', error);
        }
    }

    async syncConnectionToServer(connection, action) {
        try {
            const connData = {
                id: connection.id,
                sourceNodeId: connection.output.node.id,
                sourcePortId: 0, // Simplified for demo
                targetNodeId: connection.input.node.id,
                targetPortId: 0  // Simplified for demo
            };

            if (action === 'create') {
                const response = await fetch('/api/connections', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(connData)
                });
                const result = await response.json();
                connection.id = result.id;
            } else if (action === 'delete') {
                await fetch(`/api/connections/${connection.id}`, {
                    method: 'DELETE'
                });
            }
        } catch (error) {
            console.error('Error syncing connection:', error);
        }
    }

    async loadGraphFromServer() {
        try {
            const response = await fetch('/api/graph');
            const data = await response.json();
            
            // Clear current graph
            this.editor.clear();
            
            // Add nodes
            for (const nodeData of data.nodes) {
                const componentKey = nodeData.type === 'input' ? 'input' : 
                                   nodeData.type === 'process' ? 'process' : 'output';
                const component = this.components[componentKey];
                const node = await component.createNode();
                
                node.id = nodeData.id;
                node.position = [nodeData.x, nodeData.y];
                node.data = { ...node.data, ...nodeData.data };
                
                this.editor.addNode(node);
            }
            
            // Add connections
            for (const connData of data.connections) {
                const sourceNode = this.editor.nodes.find(n => n.id === connData.sourceNodeId);
                const targetNode = this.editor.nodes.find(n => n.id === connData.targetNodeId);
                
                if (sourceNode && targetNode) {
                    const output = sourceNode.outputs.values().next().value;
                    const input = targetNode.inputs.values().next().value;
                    
                    if (output && input) {
                        const connection = this.editor.connect(output, input);
                        connection.id = connData.id;
                    }
                }
            }
            
        } catch (error) {
            console.error('Error loading graph:', error);
        }
    }

    async clearGraph() {
        this.editor.clear();
        // Clear on server
        await fetch('/api/graph', { method: 'DELETE' });
    }

    async saveGraph(filename) {
        try {
            const response = await fetch('/api/save', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ filename })
            });
            return response.ok;
        } catch (error) {
            console.error('Error saving graph:', error);
            return false;
        }
    }

    async loadGraph(filename) {
        try {
            const response = await fetch('/api/load', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ filename })
            });
            
            if (response.ok) {
                await this.loadGraphFromServer();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error loading graph:', error);
            return false;
        }
    }
}

// Vue.js App
const app = new Vue({
    el: '#app',
    data: {
        nodeEditor: null,
        showModal: false,
        modalTitle: '',
        modalAction: '',
        filename: '',
        currentAction: null,
        saveFormat: 'midi',
        isPlaying: false,
        isPaused: false,
        dragData: null,
        // Global project settings
        globalBPM: 120,
        globalPPQ: 960,
        // Floating menu and sidebar
        showFloatingMenu: false,
        showSidebar: false,
        sidebarMode: 'nodes', // 'nodes', 'settings', 'files'
        // Program settings
        selectedSynthesizer: 'winmm',
        primaryColor: '#6366f1',
        secondaryColor: '#8b5cf6'
    },
    mounted() {
        this.nodeEditor = new NodeEditor();
        // Apply initial color theme
        this.applyColorTheme();
    },
    methods: {
        // MIDI Load Node
        addMidiLoadNode() {
            this.nodeEditor.addNode('MIDI Load', 'midiLoad');
        },

        // Node window controls
        toggleNodeMinimize(node, el) {
            // Toggle minimized state in node data
            node.data.minimized = !node.data.minimized;

            if (node.data.minimized) {
                // Minimize node - hide content, show only header
                el.classList.add('minimized');
                const content = el.querySelector('.node-content');
                if (content) {
                    content.style.display = 'none';
                }
            } else {
                // Restore node
                el.classList.remove('minimized');
                const content = el.querySelector('.node-content');
                if (content) {
                    content.style.display = 'block';
                }
            }

            // Update the node view
            this.editor.view.updateNode(node);
        },

        toggleNodeMaximize(node, el) {
            // Toggle maximized state in node data
            node.data.maximized = !node.data.maximized;

            if (node.data.maximized) {
                // Maximize node
                el.classList.add('maximized');
                el.style.width = '80vw';
                el.style.height = '80vh';
                el.style.position = 'fixed';
                el.style.top = '10vh';
                el.style.left = '10vw';
                el.style.zIndex = '1000';
            } else {
                // Restore node
                el.classList.remove('maximized');
                el.style.width = '';
                el.style.height = '';
                el.style.position = '';
                el.style.top = '';
                el.style.left = '';
                el.style.zIndex = '';
            }

            // Update the node view
            this.editor.view.updateNode(node);
        },

        // Global project settings
        updateGlobalBPM() {
            console.log('Global BPM updated to:', this.globalBPM);
            // TODO: Update playback engine with new BPM
        },

        updateGlobalPPQ() {
            console.log('Global PPQ updated to:', this.globalPPQ);
            // TODO: Update playback engine with new PPQ
        },

        // Floating menu functionality
        toggleFloatingMenu() {
            this.showFloatingMenu = !this.showFloatingMenu;
        },

        openSidebar(mode) {
            this.sidebarMode = mode;
            this.showSidebar = true;
            this.showFloatingMenu = false;
        },

        closeSidebar() {
            this.showSidebar = false;
        },

        // Program settings
        updateSynthesizer() {
            console.log('Synthesizer changed to:', this.selectedSynthesizer);
            // TODO: Implement synthesizer switching
            if (this.selectedSynthesizer === 'kdmapi') {
                console.log('KDMAPI synthesizer selected - enhanced MIDI output');
            } else {
                console.log('Windows GS Wavetable selected - standard MIDI output');
            }
        },

        updateColorPalette() {
            console.log('Color palette updated:', this.primaryColor, this.secondaryColor);
            // Apply color palette to CSS custom properties
            document.documentElement.style.setProperty('--primary-color', this.primaryColor);
            document.documentElement.style.setProperty('--secondary-color', this.secondaryColor);

            // Update gradients throughout the interface
            this.applyColorTheme();
        },

        applyColorTheme() {
            // Create comprehensive dynamic CSS for the entire interface
            const style = document.createElement('style');
            style.id = 'dynamic-theme';

            // Remove existing dynamic theme
            const existing = document.getElementById('dynamic-theme');
            if (existing) {
                existing.remove();
            }

            // Generate color variations
            const primary = this.primaryColor;
            const secondary = this.secondaryColor;
            const primaryRGB = this.hexToRgb(primary);
            const secondaryRGB = this.hexToRgb(secondary);

            style.textContent = `
                /* Comprehensive Theme Override */

                /* Toolbar and Header */
                .toolbar {
                    background: linear-gradient(90deg, ${primary}30 0%, ${secondary}30 100%);
                    border-bottom-color: ${primary}50;
                }

                .toolbar h1 {
                    background: linear-gradient(135deg, ${secondary} 0%, ${primary} 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }

                /* All Buttons */
                .toolbar-buttons button,
                .file-btn,
                .node-button,
                .control-button,
                .modal-buttons button:first-child {
                    background: linear-gradient(135deg, ${primary} 0%, ${secondary} 100%) !important;
                    border-color: ${primary}50 !important;
                    box-shadow: 0 2px 8px ${primary}50 !important;
                }

                .toolbar-buttons button:hover,
                .file-btn:hover,
                .node-button:hover,
                .control-button:hover,
                .modal-buttons button:first-child:hover {
                    background: linear-gradient(135deg, ${secondary} 0%, ${primary} 100%) !important;
                    box-shadow: 0 4px 16px ${secondary}60 !important;
                }

                /* Floating Menu */
                .floating-btn {
                    background: linear-gradient(135deg, ${primary} 0%, ${secondary} 100%) !important;
                    box-shadow: 0 8px 32px ${primary}60 !important;
                }

                .floating-btn:hover {
                    box-shadow: 0 12px 40px ${secondary}70 !important;
                }

                .floating-btn.active {
                    background: linear-gradient(135deg, ${secondary} 0%, ${primary} 100%) !important;
                }

                .floating-menu-options {
                    border-color: ${primary}50 !important;
                }

                .menu-option:hover {
                    background: linear-gradient(90deg, ${primary}30 0%, ${secondary}20 100%) !important;
                }

                /* Sidebar */
                .sidebar-header {
                    background: linear-gradient(135deg, ${primary} 0%, ${secondary} 100%) !important;
                }

                /* Windows-Style Nodes */
                .windows-node {
                    background: linear-gradient(145deg, ${primary}40 0%, ${secondary}60 100%) !important;
                    border-color: ${secondary} !important;
                    box-shadow: 0 4px 12px ${primary}50 !important;
                }

                .windows-node:hover {
                    box-shadow: 0 8px 20px ${secondary}60 !important;
                }

                .node-header {
                    background: linear-gradient(90deg, ${primary} 0%, ${secondary} 100%) !important;
                }

                .node-btn:hover {
                    background: ${primary}30 !important;
                }

                .maximize-btn:hover {
                    background: ${primary}70 !important;
                }

                /* Legacy Nodes */
                .node {
                    border-color: ${primary}50 !important;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px ${primary}20 !important;
                }

                .node:hover {
                    border-color: ${secondary}80 !important;
                    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px ${secondary}50 !important;
                }

                .node .title {
                    background: linear-gradient(135deg, ${primary} 0%, ${secondary} 100%) !important;
                }

                /* Sockets and Connections */
                .socket {
                    border-color: ${primary}80 !important;
                }

                .socket:hover {
                    background: linear-gradient(135deg, ${primary} 0%, ${secondary} 100%) !important;
                    border-color: ${secondary} !important;
                    box-shadow: 0 4px 16px ${primary}60 !important;
                }

                .socket.used {
                    background: linear-gradient(135deg, ${secondary} 0%, ${primary} 100%) !important;
                    border-color: ${secondary} !important;
                    box-shadow: 0 0 12px ${secondary}80 !important;
                }

                /* Node Items */
                .node-item {
                    border-color: ${primary}20 !important;
                }

                .node-item:hover {
                    background: linear-gradient(135deg, ${primary}30 0%, ${secondary}20 100%) !important;
                    border-color: ${primary}50 !important;
                }

                /* Input Fields */
                .bpm-input, .ppq-input,
                .modal input,
                .setting-item select,
                .setting-item input[type="color"] {
                    border-color: ${primary}50 !important;
                }

                .bpm-input:focus, .ppq-input:focus,
                .modal input:focus,
                .setting-item select:focus,
                .setting-item input[type="color"]:focus {
                    border-color: ${secondary}80 !important;
                    box-shadow: 0 0 0 2px ${primary}20 !important;
                }

                /* Modals */
                .modal {
                    border-color: ${primary}50 !important;
                }

                /* Context Menu */
                .context-menu {
                    border-color: ${primary}50 !important;
                }

                .context-menu .item:hover {
                    background: linear-gradient(90deg, ${primary}30 0%, ${secondary}20 100%) !important;
                }

                /* Scrollbars */
                ::-webkit-scrollbar-thumb {
                    background: linear-gradient(135deg, ${primary} 0%, ${secondary} 100%) !important;
                }

                ::-webkit-scrollbar-thumb:hover {
                    background: linear-gradient(135deg, ${secondary} 0%, ${primary} 100%) !important;
                }

                /* Piano Roll */
                .piano-key:hover {
                    background: linear-gradient(90deg, ${secondary} 0%, ${primary} 100%) !important;
                }

                .piano-controls button {
                    background: linear-gradient(135deg, ${primary} 0%, ${secondary} 100%) !important;
                    box-shadow: 0 2px 8px ${primary}50 !important;
                }

                .piano-controls button:hover {
                    background: linear-gradient(135deg, ${secondary} 0%, ${primary} 100%) !important;
                    box-shadow: 0 4px 16px ${secondary}60 !important;
                }

                /* Grid Pattern */
                #editor {
                    background-image: radial-gradient(circle, ${secondary}60 1px, transparent 1px) !important;
                }

                /* Connection Gradients */
                #connectionGradient stop:first-child {
                    stop-color: ${primary} !important;
                }

                #connectionGradient stop:last-child {
                    stop-color: ${secondary} !important;
                }

                #selectedConnectionGradient stop:first-child {
                    stop-color: ${secondary} !important;
                }

                #selectedConnectionGradient stop:last-child {
                    stop-color: ${primary} !important;
                }
            `;

            document.head.appendChild(style);
        },

        // Helper function to convert hex to RGB
        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        },

        resetColors() {
            this.primaryColor = '#6366f1';
            this.secondaryColor = '#8b5cf6';
            this.updateColorPalette();
        },

        clearGraph() {
            if (confirm('Are you sure you want to clear the graph?')) {
                this.nodeEditor.clearGraph();
            }
        },



        // File Management
        loadMidiFile() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.mid,.midi';
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (file) {
                    await this.uploadAndLoadMidi(file);
                }
            };
            input.click();
        },

        loadWeaveFile() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.weave';
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (file) {
                    await this.loadWeaveProject(file);
                }
            };
            input.click();
        },

        showSaveAsMenu() {
            this.modalTitle = 'Save Project As';
            this.modalAction = 'Save';
            this.currentAction = 'save-as';
            this.filename = 'project';
            this.saveFormat = 'midi';
            this.showModal = true;
        },

        // Transport Controls
        playSequence() {
            if (this.isPaused) {
                this.resumeSequence();
            } else {
                this.startSequence();
            }
        },

        pauseSequence() {
            this.isPaused = true;
            this.isPlaying = false;
            console.log('Sequence paused');
            // TODO: Implement pause logic
        },

        stopSequence() {
            this.isPlaying = false;
            this.isPaused = false;
            console.log('Sequence stopped');
            // TODO: Implement stop logic
        },

        async startSequence() {
            this.isPlaying = true;
            this.isPaused = false;
            console.log('Sequence started');

            try {
                // Get MIDI data from the server
                const response = await fetch('/api/midi-range?start=0&end=100000&max=10000');
                const midiData = await response.json();

                if (midiData.notes && midiData.notes.length > 0) {
                    await this.playMidiSequence(midiData.notes);
                } else {
                    alert('No MIDI data to play. Load a MIDI file first.');
                    this.stopSequence();
                }
            } catch (error) {
                console.error('Error starting sequence:', error);
                this.stopSequence();
            }
        },

        resumeSequence() {
            this.isPlaying = true;
            this.isPaused = false;
            console.log('Sequence resumed');
            // Continue from current position
            this.startSequence();
        },

        async playMidiSequence(notes) {
            console.log(`Playing ${notes.length} notes`);

            // Sort notes by start time
            const sortedNotes = notes.sort((a, b) => a.start - b.start);

            let currentTime = 0;
            const startTime = Date.now();
            const tempo = 120; // BPM
            const ticksPerQuarter = 480;
            const msPerTick = (60000 / tempo) / ticksPerQuarter;

            for (const note of sortedNotes) {
                if (!this.isPlaying) break;

                // Calculate when to play this note
                const noteTime = note.start * msPerTick;
                const delay = noteTime - (Date.now() - startTime);

                if (delay > 0) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                }

                if (!this.isPlaying) break;

                // Play the note
                await this.playNote(note.note, note.velocity);

                // Schedule note off
                const duration = (note.end - note.start) * msPerTick;
                setTimeout(() => {
                    this.stopNote(note.note);
                }, Math.max(duration, 100)); // Minimum 100ms duration
            }

            // Sequence finished
            if (this.isPlaying) {
                this.stopSequence();
            }
        },

        async playNote(note, velocity = 100) {
            try {
                await fetch('/api/midi-play-note', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ note: note, velocity: velocity })
                });
            } catch (error) {
                console.error('Error playing note:', error);
            }
        },

        async stopNote(note) {
            try {
                await fetch('/api/midi-stop-note', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ note: note })
                });
            } catch (error) {
                console.error('Error stopping note:', error);
            }
        },
        async confirmAction() {
            if (this.currentAction === 'save-as') {
                const success = await this.saveProjectAs(this.filename, this.saveFormat);
                if (success) {
                    alert(`Project saved as ${this.filename}.${this.saveFormat === 'midi' ? 'mid' : 'weave'}`);
                } else {
                    alert('Failed to save project.');
                }
            }
            this.closeModal();
        },

        // File operation helpers
        async uploadAndLoadMidi(file) {
            try {
                console.log('Uploading MIDI file:', file.name);

                // First upload the file
                const formData = new FormData();
                formData.append('file', file);
                formData.append('filename', file.name);

                const uploadResponse = await fetch('/api/upload-midi', {
                    method: 'POST',
                    body: formData
                });

                console.log('Upload response status:', uploadResponse.status);

                if (uploadResponse.ok) {
                    // Now load the uploaded file
                    const loadResponse = await fetch('/api/midi-load', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ filename: file.name })
                    });

                    console.log('Load response status:', loadResponse.status);

                    if (loadResponse.ok) {
                        const result = await loadResponse.json();
                        console.log('Load result:', result);

                        if (result.success) {
                            alert(`MIDI file "${file.name}" loaded: ${result.note_count} notes in ${result.parse_time_ms.toFixed(2)}ms`);
                            // Add a MIDI Input node automatically
                            this.addMidiInputNode();
                        } else {
                            alert('Failed to load MIDI file: ' + result.error);
                        }
                    } else {
                        const errorText = await loadResponse.text();
                        console.error('Load response error:', errorText);
                        alert('Failed to load MIDI file: ' + errorText);
                    }
                } else {
                    const errorText = await uploadResponse.text();
                    console.error('Upload response error:', errorText);
                    alert('Failed to upload MIDI file: ' + errorText);
                }
            } catch (error) {
                console.error('Error loading MIDI file:', error);
                alert('Error loading MIDI file: ' + error.message);
            }
        },

        async loadWeaveProject(file) {
            try {
                const text = await file.text();
                const projectData = JSON.parse(text);

                // Validate WEAVE format
                if (projectData.format !== 'WEAVE') {
                    throw new Error('Invalid WEAVE file format');
                }

                // Clear current graph
                this.nodeEditor.clearGraph();

                // Load nodes
                if (projectData.project && projectData.project.nodes) {
                    for (const nodeData of projectData.project.nodes) {
                        await this.createNodeFromData(nodeData);
                    }
                }

                // Load connections
                if (projectData.project && projectData.project.connections) {
                    for (const connData of projectData.project.connections) {
                        await this.createConnectionFromData(connData);
                    }
                }

                // Load MIDI data
                if (projectData.project && projectData.project.midi_data) {
                    await this.loadMidiDataFromWeave(projectData.project.midi_data);
                }

                alert(`WEAVE project "${projectData.metadata.name}" loaded successfully!`);
            } catch (error) {
                console.error('Error loading WEAVE project:', error);
                alert('Error loading WEAVE project: ' + error.message);
            }
        },

        async saveProjectAs(filename, format) {
            try {
                if (format === 'midi') {
                    return await this.exportToMidi(filename);
                } else if (format === 'weave') {
                    return await this.saveAsWeave(filename);
                }
                return false;
            } catch (error) {
                console.error('Error saving project:', error);
                return false;
            }
        },

        async exportToMidi(filename) {
            // TODO: Implement MIDI export
            console.log('Exporting to MIDI:', filename);
            return true;
        },

        async saveAsWeave(filename) {
            try {
                const editorData = this.nodeEditor.editor.toJSON();
                const now = new Date().toISOString();

                const projectData = {
                    format: 'WEAVE',
                    version: '1.0',
                    timestamp: now,
                    metadata: {
                        name: filename,
                        description: 'Pattern Weaver MIDI Node Editor Project',
                        author: 'Pattern Weaver User',
                        created: now,
                        modified: now,
                        tags: ['midi', 'node-editor']
                    },
                    project: {
                        nodes: this.serializeNodes(editorData.nodes),
                        connections: this.serializeConnections(),
                        midi_data: await this.extractMidiData(),
                        settings: {
                            zoom: 1.0,
                            pan: { x: 0, y: 0 },
                            theme: 'dark-purple'
                        }
                    }
                };

                const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename + '.weave';
                a.click();
                URL.revokeObjectURL(url);

                return true;
            } catch (error) {
                console.error('Error saving WEAVE project:', error);
                return false;
            }
        },

        // WEAVE format helper methods
        serializeNodes(nodes) {
            return Object.values(nodes).map(node => ({
                id: node.id,
                type: node.name.toLowerCase().replace(' ', '_'),
                name: node.name,
                position: { x: node.position[0], y: node.position[1] },
                data: node.data || {},
                inputs: Object.keys(node.inputs || {}),
                outputs: Object.keys(node.outputs || {})
            }));
        },

        serializeConnections() {
            const connections = [];
            // Extract connections from the editor
            // This is a simplified version - in a real implementation,
            // you'd iterate through all connections in the editor
            return connections;
        },

        async extractMidiData() {
            try {
                const response = await fetch('/api/midi-stats');
                const stats = await response.json();

                if (stats.total_notes > 0) {
                    const midiResponse = await fetch('/api/midi-range?start=0&end=100000&max=100000');
                    const midiData = await midiResponse.json();

                    return {
                        tracks: [{
                            id: 'main-track',
                            name: 'Main Track',
                            channel: 0,
                            notes: midiData.notes || [],
                            events: []
                        }],
                        tempo: 120,
                        time_signature: [4, 4],
                        ticks_per_quarter: 480
                    };
                }

                return {
                    tracks: [],
                    tempo: 120,
                    time_signature: [4, 4],
                    ticks_per_quarter: 480
                };
            } catch (error) {
                console.error('Error extracting MIDI data:', error);
                return { tracks: [], tempo: 120, time_signature: [4, 4], ticks_per_quarter: 480 };
            }
        },

        async createNodeFromData(nodeData) {
            const componentMap = {
                'midi_input': 'midiInput',
                'piano_roll': 'pianoRoll',
                'midi_filter': 'midiFilter',
                'midi_transform': 'midiTransform',
                'midi_visualizer': 'midiVisualizer',
                'midi_output': 'midiOutput',
                'input': 'input',
                'process': 'process',
                'output': 'output'
            };

            const componentKey = componentMap[nodeData.type];
            if (componentKey) {
                const node = await this.nodeEditor.addNode(nodeData.name, componentKey);
                node.position = [nodeData.position.x, nodeData.position.y];
                node.data = { ...node.data, ...nodeData.data };
            }
        },

        async createConnectionFromData(connData) {
            // TODO: Implement connection recreation
            console.log('Creating connection:', connData);
        },

        async loadMidiDataFromWeave(midiData) {
            if (midiData.tracks && midiData.tracks.length > 0) {
                // TODO: Load MIDI data into the engine
                console.log('Loading MIDI data:', midiData);
            }
        },
        closeModal() {
            this.showModal = false;
            this.filename = '';
            this.currentAction = null;
        },

        // High-Performance Drag and Drop System
        startDrag(event, nodeName, componentKey) {
            console.log(`Starting drag for ${nodeName}`);
            this.dragData = { nodeName, componentKey };

            // Optimize drag data transfer
            const dragData = JSON.stringify({ nodeName, componentKey });
            event.dataTransfer.setData('application/json', dragData);
            event.dataTransfer.setData('text/plain', dragData); // Fallback
            event.dataTransfer.effectAllowed = 'copy';

            // Add visual feedback
            event.target.style.opacity = '0.7';
        },

        handleDrop(event) {
            event.preventDefault();
            console.log('Drop event triggered');

            // Restore visual feedback
            document.querySelectorAll('.node-item').forEach(item => {
                item.style.opacity = '1';
            });

            if (!this.dragData) {
                console.log('No drag data available');
                return;
            }

            console.log('Drag data:', this.dragData);

            // Optimized position calculation
            const editorRect = document.getElementById('editor').getBoundingClientRect();
            const x = Math.round(event.clientX - editorRect.left);
            const y = Math.round(event.clientY - editorRect.top);

            console.log(`Drop position: (${x}, ${y})`);

            // Use requestAnimationFrame for smooth node creation
            requestAnimationFrame(() => {
                this.createNodeAtPosition(this.dragData.nodeName, this.dragData.componentKey, x, y);
                this.dragData = null;
            });
        },

        async createNodeAtPosition(nodeName, componentKey, x, y) {
            try {
                console.log(`Attempting to create ${nodeName} node at (${x}, ${y})`);

                const component = this.nodeEditor.components[componentKey];
                if (!component) {
                    console.error('Component not found:', componentKey);
                    console.log('Available components:', Object.keys(this.nodeEditor.components));
                    return;
                }

                console.log('Component found:', component.name);

                // Optimized node creation
                const node = await this.nodeEditor.addNode(nodeName, componentKey);
                if (node) {
                    // Set position immediately for better performance
                    node.position = [x, y];

                    // Batch DOM updates for better performance
                    requestAnimationFrame(() => {
                        this.nodeEditor.editor.view.resize();
                        this.nodeEditor.editor.trigger('process');
                    });

                    console.log(`Successfully created ${nodeName} node at (${x}, ${y})`);
                } else {
                    console.error('Failed to create node');
                }
            } catch (error) {
                console.error('Error creating node:', error);
            }
        },


    }
});
