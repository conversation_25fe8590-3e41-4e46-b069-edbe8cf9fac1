// Main Application
class NodeEditor {
    constructor() {
        this.editor = null;
        this.engine = null;
        this.components = {};
        this.init();
    }

    async init() {
        const container = document.querySelector('#editor');
        
        // Create editor and engine
        this.editor = new Rete.NodeEditor('demo@0.1.0', container);
        this.engine = new Rete.Engine('demo@0.1.0');
        
        // Register components
        this.components.input = new NodeComponents.InputComponent();
        this.components.process = new NodeComponents.ProcessComponent();
        this.components.output = new NodeComponents.OutputComponent();

        // MIDI components
        this.components.midiInput = new NodeComponents.MidiFileInputComponent();
        this.components.midiFilter = new NodeComponents.MidiFilterComponent();
        this.components.midiTransform = new NodeComponents.MidiTransformComponent();
        this.components.midiVisualizer = new NodeComponents.MidiVisualizerComponent();
        this.components.midiOutput = new NodeComponents.MidiOutputComponent();

        // Register all components
        Object.values(this.components).forEach(component => {
            this.editor.register(component);
            this.engine.register(component);
        });
        
        // Use plugins
        this.editor.use(ConnectionPlugin.default);
        this.editor.use(VueRenderPlugin.default);
        this.editor.use(ContextMenuPlugin.default, {
            'MIDI File Input': () => this.addNode('MIDI File Input', 'midiInput'),
            'MIDI Filter': () => this.addNode('MIDI Filter', 'midiFilter'),
            'MIDI Transform': () => this.addNode('MIDI Transform', 'midiTransform'),
            'MIDI Visualizer': () => this.addNode('MIDI Visualizer', 'midiVisualizer'),
            'MIDI Output': () => this.addNode('MIDI Output', 'midiOutput'),
            '---': null,
            'Add Input': () => this.addNode('Input', 'input'),
            'Add Process': () => this.addNode('Process', 'process'),
            'Add Output': () => this.addNode('Output', 'output'),
            '---2': null,
            'Delete': () => this.deleteSelected()
        });
        this.editor.use(AreaPlugin);
        
        // Event listeners
        this.setupEventListeners();
        
        // Load initial graph
        await this.loadGraphFromServer();
        
        // Trigger initial processing
        this.editor.view.resize();
        this.editor.trigger('process');
    }

    setupEventListeners() {
        // Node events
        this.editor.on('nodecreated', async (node) => {
            await this.syncNodeToServer(node, 'create');
        });

        this.editor.on('noderemoved', async (node) => {
            await this.syncNodeToServer(node, 'delete');
        });

        this.editor.on('connectioncreated', async (connection) => {
            await this.syncConnectionToServer(connection, 'create');
        });

        this.editor.on('connectionremoved', async (connection) => {
            await this.syncConnectionToServer(connection, 'delete');
        });

        // Process events
        this.editor.on('process nodecreated noderemoved connectioncreated connectionremoved', async () => {
            await this.engine.process(this.editor.toJSON());
        });
    }

    async addNode(type, componentKey) {
        const component = this.components[componentKey];
        const node = await component.createNode();
        
        // Position node at center of view
        const area = this.editor.view.area;
        const rect = area.el.getBoundingClientRect();
        const [x, y] = area.pointermove.getCoords({ 
            clientX: rect.width / 2, 
            clientY: rect.height / 2 
        });
        
        node.position = [x, y];
        node.data.type = type.toLowerCase();
        
        this.editor.addNode(node);
        return node;
    }

    deleteSelected() {
        const selected = this.editor.selected.list;
        selected.forEach(item => {
            if (item instanceof Rete.Node) {
                this.editor.removeNode(item);
            } else if (item instanceof Rete.Connection) {
                this.editor.removeConnection(item);
            }
        });
    }

    async syncNodeToServer(node, action) {
        try {
            const nodeData = {
                id: node.id,
                type: node.data.type || node.name.toLowerCase(),
                name: node.name,
                x: node.position[0],
                y: node.position[1],
                data: node.data
            };

            if (action === 'create') {
                const response = await fetch('/api/nodes', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(nodeData)
                });
                const result = await response.json();
                node.id = result.id;
            } else if (action === 'delete') {
                await fetch(`/api/nodes/${node.id}`, {
                    method: 'DELETE'
                });
            }
        } catch (error) {
            console.error('Error syncing node:', error);
        }
    }

    async syncConnectionToServer(connection, action) {
        try {
            const connData = {
                id: connection.id,
                sourceNodeId: connection.output.node.id,
                sourcePortId: 0, // Simplified for demo
                targetNodeId: connection.input.node.id,
                targetPortId: 0  // Simplified for demo
            };

            if (action === 'create') {
                const response = await fetch('/api/connections', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(connData)
                });
                const result = await response.json();
                connection.id = result.id;
            } else if (action === 'delete') {
                await fetch(`/api/connections/${connection.id}`, {
                    method: 'DELETE'
                });
            }
        } catch (error) {
            console.error('Error syncing connection:', error);
        }
    }

    async loadGraphFromServer() {
        try {
            const response = await fetch('/api/graph');
            const data = await response.json();
            
            // Clear current graph
            this.editor.clear();
            
            // Add nodes
            for (const nodeData of data.nodes) {
                const componentKey = nodeData.type === 'input' ? 'input' : 
                                   nodeData.type === 'process' ? 'process' : 'output';
                const component = this.components[componentKey];
                const node = await component.createNode();
                
                node.id = nodeData.id;
                node.position = [nodeData.x, nodeData.y];
                node.data = { ...node.data, ...nodeData.data };
                
                this.editor.addNode(node);
            }
            
            // Add connections
            for (const connData of data.connections) {
                const sourceNode = this.editor.nodes.find(n => n.id === connData.sourceNodeId);
                const targetNode = this.editor.nodes.find(n => n.id === connData.targetNodeId);
                
                if (sourceNode && targetNode) {
                    const output = sourceNode.outputs.values().next().value;
                    const input = targetNode.inputs.values().next().value;
                    
                    if (output && input) {
                        const connection = this.editor.connect(output, input);
                        connection.id = connData.id;
                    }
                }
            }
            
        } catch (error) {
            console.error('Error loading graph:', error);
        }
    }

    async clearGraph() {
        this.editor.clear();
        // Clear on server
        await fetch('/api/graph', { method: 'DELETE' });
    }

    async saveGraph(filename) {
        try {
            const response = await fetch('/api/save', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ filename })
            });
            return response.ok;
        } catch (error) {
            console.error('Error saving graph:', error);
            return false;
        }
    }

    async loadGraph(filename) {
        try {
            const response = await fetch('/api/load', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ filename })
            });
            
            if (response.ok) {
                await this.loadGraphFromServer();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error loading graph:', error);
            return false;
        }
    }
}

// Vue.js App
const app = new Vue({
    el: '#app',
    data: {
        nodeEditor: null,
        showModal: false,
        modalTitle: '',
        modalAction: '',
        filename: '',
        currentAction: null
    },
    mounted() {
        this.nodeEditor = new NodeEditor();
    },
    methods: {
        // MIDI-specific methods
        addMidiInputNode() {
            this.nodeEditor.addNode('MIDI File Input', 'midiInput');
        },
        addMidiFilterNode() {
            this.nodeEditor.addNode('MIDI Filter', 'midiFilter');
        },
        addMidiTransformNode() {
            this.nodeEditor.addNode('MIDI Transform', 'midiTransform');
        },
        addMidiVisualizerNode() {
            this.nodeEditor.addNode('MIDI Visualizer', 'midiVisualizer');
        },
        addMidiOutputNode() {
            this.nodeEditor.addNode('MIDI Output', 'midiOutput');
        },

        // Original methods (kept for compatibility)
        addInputNode() {
            this.nodeEditor.addNode('Input', 'input');
        },
        addProcessNode() {
            this.nodeEditor.addNode('Process', 'process');
        },
        addOutputNode() {
            this.nodeEditor.addNode('Output', 'output');
        },
        clearGraph() {
            if (confirm('Are you sure you want to clear the graph?')) {
                this.nodeEditor.clearGraph();
            }
        },
        saveGraph() {
            this.modalTitle = 'Save Graph';
            this.modalAction = 'Save';
            this.currentAction = 'save';
            this.filename = 'graph.json';
            this.showModal = true;
        },
        loadGraph() {
            this.modalTitle = 'Load Graph';
            this.modalAction = 'Load';
            this.currentAction = 'load';
            this.filename = 'graph.json';
            this.showModal = true;
        },
        async confirmAction() {
            if (this.currentAction === 'save') {
                const success = await this.nodeEditor.saveGraph(this.filename);
                if (success) {
                    alert('Graph saved successfully!');
                } else {
                    alert('Failed to save graph.');
                }
            } else if (this.currentAction === 'load') {
                const success = await this.nodeEditor.loadGraph(this.filename);
                if (success) {
                    alert('Graph loaded successfully!');
                } else {
                    alert('Failed to load graph.');
                }
            }
            this.closeModal();
        },
        closeModal() {
            this.showModal = false;
            this.filename = '';
            this.currentAction = null;
        }
    }
});
