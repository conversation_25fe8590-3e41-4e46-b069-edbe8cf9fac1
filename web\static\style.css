* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #e0e6ed;
    height: 100vh;
    overflow: hidden;
    /* Performance optimizations for professional use */
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.toolbar {
    background: linear-gradient(90deg, #1a1a2e 0%, #16213e 100%);
    padding: 6px 24px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    min-height: 45px;
    gap: 20px;
}

/* Global Project Settings */
.global-settings {
    display: flex;
    gap: 16px;
    align-items: center;
}

.setting-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

.setting-group label {
    color: #e0e6ed;
    font-size: 12px;
    font-weight: 500;
    min-width: 35px;
}

.bpm-input, .ppq-input {
    width: 70px;
    padding: 4px 8px;
    background: rgba(15, 15, 35, 0.6);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 4px;
    color: #e0e6ed;
    font-size: 12px;
    text-align: center;
}

.bpm-input:focus, .ppq-input:focus {
    outline: none;
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.toolbar h1 {
    font-size: 1.2em;
    font-weight: 600;
    background: linear-gradient(135deg, #a855f7 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.toolbar-buttons {
    display: flex;
    gap: 16px;
    align-items: center;
}

.button-group {
    display: flex;
    flex-direction: row;
    gap: 12px;
    align-items: center;
}

.button-group label {
    font-size: 11px;
    color: #9ca3af;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0;
    margin-right: 6px;
    white-space: nowrap;
}

.button-group > div {
    display: flex;
    gap: 6px;
}

.button-group button {
    margin: 0 3px;
}

.toolbar-buttons button {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: #ffffff;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.toolbar-buttons button:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
    transform: translateY(-1px);
}

.toolbar-buttons button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

/* MIDI-specific button colors */
.button-group:first-child button {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    box-shadow: 0 2px 8px rgba(168, 85, 247, 0.3);
}

.button-group:first-child button:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%);
    box-shadow: 0 4px 16px rgba(168, 85, 247, 0.4);
}

#editor {
    flex: 1;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    position: relative;
    overflow: hidden;
    /* Performance optimizations for smooth node movement */
    will-change: transform;
    transform: translateZ(0);
    contain: layout style paint;

    /* Professional dotted grid pattern */
    background-image:
        radial-gradient(circle, rgba(138, 43, 226, 0.4) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Windows-Style Node Styling */
.windows-node {
    background: linear-gradient(145deg, #2d1b69 0%, #8b5cf6 100%);
    border: 2px solid #a855f7;
    border-radius: 8px;
    color: white;
    min-width: 220px;
    font-family: 'Inter', Arial, sans-serif;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    cursor: move;
    user-select: none;
    /* Performance optimizations */
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    contain: layout style paint;
    transition: transform 0.1s ease-out, box-shadow 0.2s ease;
    overflow: hidden;
}

.windows-node:hover {
    transform: translateY(-2px) translateZ(0);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4);
}

/* Windows-style header bar */
.node-header {
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    border-radius: 6px 6px 0 0;
}

/* Node title in header */
.node-title {
    font-weight: bold;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

/* Control buttons container */
.node-control-buttons {
    display: flex;
    gap: 4px;
}

/* Individual control buttons */
.node-btn {
    width: 24px;
    height: 24px;
    border-radius: 3px;
    border: 1px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.node-btn:hover {
    background: rgba(255,255,255,0.2);
}

.minimize-btn:hover {
    background: rgba(255,255,255,0.3);
}

.maximize-btn:hover {
    background: rgba(0,120,215,0.7);
}

.close-btn {
    background: rgba(232,17,35,0.6);
}

.close-btn:hover {
    background: rgba(232,17,35,0.9);
}

/* Node content container */
.node-content {
    padding: 12px;
}

/* Minimized state */
.windows-node.minimized {
    min-height: unset;
    height: auto;
}

.windows-node.minimized .node-content {
    display: none;
}

/* Maximized state */
.windows-node.maximized {
    position: absolute;
    width: 90% !important;
    height: 90% !important;
    top: 5%;
    left: 5%;
    z-index: 1000;
    transform: none !important;
}

.windows-node.maximized .node-content {
    height: calc(100% - 37px);
    overflow: auto;
}

.node-inputs {
    margin-bottom: 6px;
}

.node-input-item {
    font-size: 12px;
    margin-bottom: 2px;
    opacity: 0.8;
}

.node-outputs {
    margin-bottom: 6px;
}

.node-output-item {
    font-size: 12px;
    margin-bottom: 2px;
    opacity: 0.8;
    text-align: right;
}

.node-controls {
    margin-top: 8px;
    border-top: 1px solid rgba(255,255,255,0.2);
    padding-top: 6px;
}

.node-control-item {
    font-size: 11px;
    margin-bottom: 2px;
    opacity: 0.7;
}

/* Legacy node styling for compatibility */
.node {
    background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 0 0 12px 0;
    min-width: 180px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(139, 92, 246, 0.1);
    transition: box-shadow 0.2s ease, border-color 0.2s ease;
    backdrop-filter: blur(10px);
    cursor: move;
    user-select: none;
}

.node:hover {
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(139, 92, 246, 0.3);
    transform: translateY(-2px);
}

.node.selected {
    border-color: rgba(168, 85, 247, 0.8);
    box-shadow: 0 12px 40px rgba(168, 85, 247, 0.3), 0 0 0 2px rgba(168, 85, 247, 0.5);
}

.node .title {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: #ffffff;
    padding: 12px 16px;
    margin: 0;
    border-radius: 12px 12px 0 0;
    font-weight: 600;
    text-align: center;
    font-size: 13px;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
}

.node .input,
.node .output {
    margin: 10px 0;
    display: flex;
    align-items: center;
    padding: 0 16px;
    min-height: 24px;
}

.node .input {
    justify-content: flex-start;
}

.node .output {
    justify-content: flex-end;
}

.node .input-title,
.node .output-title {
    margin: 0 12px;
    color: #cbd5e1;
    font-size: 12px;
    font-weight: 500;
}

.socket {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid rgba(139, 92, 246, 0.6);
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.socket:hover {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-color: rgba(168, 85, 247, 0.8);
    transform: scale(1.3);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.socket.used {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    border-color: rgba(168, 85, 247, 1);
    box-shadow: 0 0 12px rgba(168, 85, 247, 0.6);
}

/* Connection Styling */
.connection {
    stroke: url(#connectionGradient);
    stroke-width: 2.5px;
    fill: none;
    pointer-events: none;
    filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.3));
}

.connection.selected {
    stroke: url(#selectedConnectionGradient);
    stroke-width: 3px;
    filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.6));
}

/* Context Menu */
.context-menu {
    background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
    padding: 8px 0;
    min-width: 180px;
    backdrop-filter: blur(10px);
}

.context-menu .item {
    padding: 12px 20px;
    cursor: pointer;
    color: #e0e6ed;
    transition: all 0.2s ease;
    font-size: 13px;
    font-weight: 500;
}

.context-menu .item:hover {
    background: linear-gradient(90deg, rgba(139, 92, 246, 0.2) 0%, rgba(168, 85, 247, 0.1) 100%);
    color: #ffffff;
}

/* Modal Styling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 15, 35, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal {
    background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 16px;
    padding: 24px;
    min-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
}

.modal h3 {
    margin-bottom: 20px;
    color: #e0e6ed;
    font-weight: 600;
    font-size: 18px;
}

.modal input {
    width: 100%;
    padding: 12px 16px;
    margin-bottom: 20px;
    background: rgba(15, 15, 35, 0.6);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 8px;
    color: #e0e6ed;
    font-size: 14px;
    transition: all 0.3s ease;
}

.modal input:focus {
    outline: none;
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.modal-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-buttons button {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.modal-buttons button:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.modal-buttons button:last-child {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    box-shadow: 0 2px 8px rgba(100, 116, 139, 0.3);
}

.modal-buttons button:last-child:hover {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
    box-shadow: 0 4px 16px rgba(100, 116, 139, 0.4);
}

/* Node Types */
.node.input .title {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.node.process .title {
    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
}

.node.output .title {
    background: linear-gradient(135deg, #ec4899 0%, #f97316 100%);
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: rgba(15, 15, 35, 0.3);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-radius: 5px;
    border: 2px solid rgba(15, 15, 35, 0.3);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
}

/* Piano Roll Styling */
.piano-roll-control {
    width: 100%;
    max-width: 900px;
}

.piano-roll-container {
    display: flex;
    background: linear-gradient(145deg, #0f0f23 0%, #1a1a2e 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 8px;
    overflow: hidden;
    margin: 8px 0;
}

.piano-keys {
    width: 80px;
    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
    border-right: 1px solid rgba(139, 92, 246, 0.3);
    overflow-y: auto;
    max-height: 400px;
}

.piano-key {
    height: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

.piano-key.white {
    background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 100%);
    color: #1e293b;
}

.piano-key.black {
    background: linear-gradient(90deg, #1e293b 0%, #334155 100%);
    color: #e2e8f0;
}

.piano-key:hover {
    background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 100%);
    color: #ffffff;
    transform: scaleX(1.1);
}

.piano-grid {
    flex: 1;
    position: relative;
    overflow: auto;
    max-height: 400px;
}

.piano-grid canvas {
    display: block;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    cursor: crosshair;
}

.piano-controls {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.piano-controls button {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: #ffffff;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.piano-controls button:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.piano-controls button:active {
    transform: translateY(0);
}

/* Node button styling */
.node-button {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: #ffffff;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    margin: 4px 0;
}

.node-button:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.control-button {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    width: 100%;
}

.control-button:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

/* Transport Controls */
.button-group button:disabled {
    background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
    cursor: not-allowed;
    opacity: 0.6;
}

.button-group button:disabled:hover {
    transform: none;
    box-shadow: 0 2px 8px rgba(75, 85, 99, 0.3);
}

/* Modal Enhancements */
.format-selection {
    margin: 16px 0;
}

.format-selection label {
    display: block;
    margin-bottom: 8px;
    color: #e2e8f0;
    font-weight: 500;
}

.format-selection select {
    width: 100%;
    padding: 8px 12px;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 6px;
    color: #e2e8f0;
    font-size: 14px;
}

.format-selection select:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* Drag and Drop Styling */
.node-item[draggable="true"] {
    cursor: grab;
    transition: all 0.2s ease;
}

.node-item[draggable="true"]:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.node-item[draggable="true"]:active {
    cursor: grabbing;
    transform: scale(0.95);
}

#editor {
    position: relative;
}

#editor.drag-over {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
    border: 2px dashed rgba(139, 92, 246, 0.5);
}

/* Add gradient definitions for connections */
svg defs {
    position: absolute;
    width: 0;
    height: 0;
}

/* Sidebar Components */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.sidebar {
    width: 320px;
    background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
    border-right: 1px solid rgba(139, 92, 246, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
}

.sidebar-header {
    padding: 16px 24px;
    background: linear-gradient(90deg, #16213e 0%, #1a1a2e 100%);
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
    font-weight: 600;
    font-size: 14px;
    color: #e0e6ed;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.node-category {
    margin-bottom: 24px;
}

.node-category h4 {
    color: #cbd5e1;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 12px;
    padding-left: 8px;
}

.node-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.node-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.3) 100%);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    font-weight: 500;
    color: #cbd5e1;
}

.node-item:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(168, 85, 247, 0.1) 100%);
    border-color: rgba(139, 92, 246, 0.3);
    transform: translateX(4px);
    color: #e0e6ed;
}

.node-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* Floating Menu */
.floating-menu {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
}

.floating-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(139, 92, 246, 0.5);
}

.floating-btn.active {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    transform: rotate(90deg);
}

.floating-menu-options {
    position: absolute;
    bottom: 70px;
    left: 0;
    background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 8px;
    min-width: 150px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.menu-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: none;
    border: none;
    color: #e0e6ed;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    width: 100%;
    text-align: left;
    font-size: 14px;
}

.menu-option:hover {
    background: linear-gradient(90deg, rgba(139, 92, 246, 0.2) 0%, rgba(168, 85, 247, 0.1) 100%);
    color: #ffffff;
}

.menu-icon {
    font-size: 16px;
}

/* Enhanced Sidebar */
.sidebar-hidden {
    transform: translateX(-100%);
}

.sidebar-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Settings Panel */
.settings-panel {
    padding: 20px;
}

.setting-section {
    margin-bottom: 24px;
}

.setting-section h4 {
    color: #e0e6ed;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
    padding-bottom: 6px;
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    color: #9ca3af;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 6px;
}

.setting-item select,
.setting-item input[type="color"] {
    width: 100%;
    padding: 8px 12px;
    background: rgba(15, 15, 35, 0.6);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 6px;
    color: #e0e6ed;
    font-size: 12px;
}

.setting-item input[type="color"] {
    height: 40px;
    padding: 4px;
    cursor: pointer;
}

.reset-btn {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.reset-btn:hover {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
}

/* File Explorer */
.file-explorer {
    padding: 20px;
}

.file-section {
    margin-bottom: 20px;
}

.file-section h4 {
    color: #e0e6ed;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
    padding-bottom: 6px;
}

.file-btn {
    display: block;
    width: 100%;
    padding: 12px 16px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    text-align: left;
}

.file-btn:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    transform: translateY(-1px);
}

.recent-files {
    background: rgba(15, 15, 35, 0.3);
    border-radius: 8px;
    padding: 12px;
}

/* MIDI Load Node Styles */
.midi-load-control {
    min-width: 280px;
    font-family: 'Inter', Arial, sans-serif;
}

.midi-load-compact {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.midi-status-display {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(15, 15, 35, 0.4);
    border-radius: 8px;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.status-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    background: rgba(139, 92, 246, 0.1);
    border: 2px solid rgba(139, 92, 246, 0.3);
    transition: all 0.3s ease;
}

.status-indicator.loading {
    border-color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.status-indicator.loaded {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.loading-spinner {
    animation: spin 1s linear infinite;
    color: #f59e0b;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loaded-icon {
    color: #10b981;
}

.empty-icon {
    color: #6b7280;
}

.status-text {
    flex: 1;
    color: #e0e6ed;
    font-size: 13px;
    font-weight: 500;
}

.transport-controls {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.transport-btn {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    border: none;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.transport-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    transform: translateY(-1px);
}

.transport-btn:disabled {
    background: rgba(107, 114, 128, 0.5);
    cursor: not-allowed;
    opacity: 0.5;
}

.load-section {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(139, 92, 246, 0.2);
}

.load-btn {
    width: 100%;
    padding: 10px 16px;
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.load-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%);
    transform: translateY(-1px);
}

.load-btn:disabled {
    background: rgba(107, 114, 128, 0.5);
    cursor: not-allowed;
}

.maximize-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.maximize-btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
}

/* MIDI Diagnosis Styles */
.midi-diagnosis {
    min-width: 500px;
    max-width: 700px;
    padding: 20px;
}

.diagnosis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.3);
}

.diagnosis-header h3 {
    margin: 0;
    color: #e0e6ed;
    font-size: 18px;
    font-weight: 600;
}

.minimize-btn {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    border: none;
    background: rgba(239, 68, 68, 0.8);
    color: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.minimize-btn:hover {
    background: rgba(239, 68, 68, 1);
}

.diagnosis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 24px;
}

.diagnosis-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(15, 15, 35, 0.4);
    border-radius: 8px;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.diagnosis-item label {
    color: #9ca3af;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.diagnosis-item span {
    color: #e0e6ed;
    font-size: 14px;
    font-weight: 600;
}

.advanced-transport {
    background: rgba(15, 15, 35, 0.6);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.transport-row {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.transport-row .transport-btn,
.transport-row .load-btn {
    flex: 1;
    height: 44px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
}

.playback-info {
    margin-top: 16px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(107, 114, 128, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
    border-radius: 3px;
    transition: width 0.1s ease;
}

.time-display {
    text-align: center;
    color: #9ca3af;
    font-size: 12px;
    font-weight: 500;
}
