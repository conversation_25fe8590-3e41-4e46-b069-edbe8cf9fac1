* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1e1e1e;
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.toolbar {
    background: #2d2d2d;
    padding: 10px 20px;
    border-bottom: 1px solid #404040;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.toolbar h1 {
    font-size: 1.5em;
    color: #ffffff;
}

.toolbar-buttons {
    display: flex;
    gap: 20px;
    align-items: center;
}

.button-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.button-group label {
    font-size: 12px;
    color: #cccccc;
    font-weight: bold;
    margin-bottom: 5px;
}

.button-group > div {
    display: flex;
    gap: 5px;
}

.toolbar-buttons button {
    background: #007acc;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
    white-space: nowrap;
}

.toolbar-buttons button:hover {
    background: #005a9e;
}

.toolbar-buttons button:active {
    background: #004578;
}

/* MIDI-specific button colors */
.button-group:first-child button {
    background: #4CAF50;
}

.button-group:first-child button:hover {
    background: #45a049;
}

#editor {
    flex: 1;
    background: #1e1e1e;
    position: relative;
    overflow: hidden;
}

/* Rete.js Node Styling */
.node {
    background: #2d2d2d;
    border: 2px solid #404040;
    border-radius: 8px;
    padding: 10px;
    min-width: 150px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: border-color 0.2s;
}

.node:hover {
    border-color: #007acc;
}

.node.selected {
    border-color: #00ff00;
}

.node .title {
    background: #404040;
    color: #ffffff;
    padding: 5px 10px;
    margin: -10px -10px 10px -10px;
    border-radius: 6px 6px 0 0;
    font-weight: bold;
    text-align: center;
}

.node .input,
.node .output {
    margin: 5px 0;
    display: flex;
    align-items: center;
}

.node .input {
    justify-content: flex-start;
}

.node .output {
    justify-content: flex-end;
}

.node .input-title,
.node .output-title {
    margin: 0 10px;
    color: #cccccc;
    font-size: 12px;
}

.socket {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    background: #404040;
    cursor: pointer;
    transition: all 0.2s;
}

.socket:hover {
    background: #007acc;
    transform: scale(1.2);
}

.socket.used {
    background: #00ff00;
}

/* Connection Styling */
.connection {
    stroke: #007acc;
    stroke-width: 3px;
    fill: none;
    pointer-events: none;
}

.connection.selected {
    stroke: #00ff00;
}

/* Context Menu */
.context-menu {
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    padding: 5px 0;
    min-width: 150px;
}

.context-menu .item {
    padding: 8px 15px;
    cursor: pointer;
    color: #ffffff;
    transition: background-color 0.2s;
}

.context-menu .item:hover {
    background: #404040;
}

/* Modal Styling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal {
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 8px;
    padding: 20px;
    min-width: 300px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
}

.modal h3 {
    margin-bottom: 15px;
    color: #ffffff;
}

.modal input {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    background: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
    color: #ffffff;
    font-size: 14px;
}

.modal input:focus {
    outline: none;
    border-color: #007acc;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-buttons button {
    background: #007acc;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.modal-buttons button:hover {
    background: #005a9e;
}

.modal-buttons button:last-child {
    background: #666666;
}

.modal-buttons button:last-child:hover {
    background: #555555;
}

/* Node Types */
.node.input .title {
    background: #4CAF50;
}

.node.process .title {
    background: #FF9800;
}

.node.output .title {
    background: #F44336;
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555555;
}
