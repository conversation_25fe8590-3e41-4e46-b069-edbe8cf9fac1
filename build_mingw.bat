@echo off
echo Building Web Node Editor with MinGW...

REM Check if MinGW is available
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo MinGW GCC not found in PATH!
    echo.
    echo Please install MinGW-w64 from one of these sources:
    echo 1. MSYS2: https://www.msys2.org/
    echo 2. TDM-GCC: https://jmeubank.github.io/tdm-gcc/
    echo 3. MinGW-w64: https://www.mingw-w64.org/downloads/
    echo.
    echo After installation, make sure gcc is in your PATH.
    pause
    exit /b 1
)

REM Check if CMake is available
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo CMake not found in PATH!
    echo Please install CMake from: https://cmake.org/download/
    pause
    exit /b 1
)

echo Found GCC version:
gcc --version | findstr gcc

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with MinGW
echo Configuring with MinGW Makefiles...
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo Building...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo.
echo To run the application:
echo   cd build
echo   web_node_editor.exe
echo.
echo Then open your browser to http://localhost:8080
pause
