<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MIDI Node Editor - Standalone Test</title>
    <script src="https://cdn.jsdelivr.net/npm/rete@1.4.5/build/rete.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-connection-plugin@0.9.0/build/connection-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-vue-render-plugin@0.5.1/build/vue-render-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-context-menu-plugin@0.6.0/build/context-menu-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-area-plugin@0.2.1/build/area-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        #app {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .toolbar {
            background: #2d2d2d;
            padding: 10px 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .toolbar h1 {
            font-size: 1.5em;
            color: #4CAF50;
        }

        .toolbar-buttons {
            display: flex;
            gap: 10px;
        }

        .toolbar-buttons button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .toolbar-buttons button:hover {
            background: #45a049;
        }

        #editor {
            flex: 1;
            background: #1e1e1e;
            position: relative;
            overflow: hidden;
        }

        /* Node styling */
        .node {
            background: #2d2d2d;
            border: 2px solid #404040;
            border-radius: 8px;
            padding: 10px;
            min-width: 150px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: border-color 0.2s;
        }

        .node:hover {
            border-color: #4CAF50;
        }

        .node .title {
            background: #4CAF50;
            color: #ffffff;
            padding: 5px 10px;
            margin: -10px -10px 10px -10px;
            border-radius: 6px 6px 0 0;
            font-weight: bold;
            text-align: center;
        }

        .socket {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #ffffff;
            background: #404040;
            cursor: pointer;
            transition: all 0.2s;
        }

        .socket:hover {
            background: #4CAF50;
            transform: scale(1.2);
        }

        .connection {
            stroke: #4CAF50;
            stroke-width: 3px;
            fill: none;
        }

        .status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(45, 45, 45, 0.9);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="toolbar">
            <h1>🎵 MIDI Node Editor - Standalone Test</h1>
            <div class="toolbar-buttons">
                <button @click="addMidiInput">MIDI Input</button>
                <button @click="addMidiFilter">Filter</button>
                <button @click="addMidiOutput">Output</button>
                <button @click="clearNodes">Clear</button>
            </div>
        </div>
        <div id="editor"></div>
        <div class="status">
            Standalone Mode - No Backend Required<br>
            Nodes: {{ nodeCount }} | Connections: {{ connectionCount }}
        </div>
    </div>

    <script>
        // Simple MIDI node components for standalone testing
        class MidiInputComponent extends Rete.Component {
            constructor() {
                super("MIDI Input");
            }

            builder(node) {
                const out1 = new Rete.Output("midi", "MIDI Data", midiSocket);
                return node.addOutput(out1);
            }

            worker(node, inputs, outputs) {
                outputs["midi"] = { type: "midi_data", notes: 1000 };
            }
        }

        class MidiFilterComponent extends Rete.Component {
            constructor() {
                super("MIDI Filter");
            }

            builder(node) {
                const inp1 = new Rete.Input("midi", "MIDI Input", midiSocket);
                const out1 = new Rete.Output("midi", "Filtered MIDI", midiSocket);
                return node.addInput(inp1).addOutput(out1);
            }

            worker(node, inputs, outputs) {
                const input = inputs["midi"] ? inputs["midi"][0] : null;
                if (input) {
                    outputs["midi"] = { ...input, filtered: true };
                }
            }
        }

        class MidiOutputComponent extends Rete.Component {
            constructor() {
                super("MIDI Output");
            }

            builder(node) {
                const inp1 = new Rete.Input("midi", "MIDI Input", midiSocket);
                return node.addInput(inp1);
            }

            worker(node, inputs, outputs) {
                const input = inputs["midi"] ? inputs["midi"][0] : null;
                if (input) {
                    console.log("MIDI Output:", input);
                }
            }
        }

        // Socket definition
        const midiSocket = new Rete.Socket("MIDI data");

        // Vue app
        const app = new Vue({
            el: '#app',
            data: {
                editor: null,
                engine: null,
                nodeCount: 0,
                connectionCount: 0
            },
            async mounted() {
                await this.initEditor();
            },
            methods: {
                async initEditor() {
                    const container = document.querySelector('#editor');
                    
                    this.editor = new Rete.NodeEditor('midi-test@1.0.0', container);
                    this.engine = new Rete.Engine('midi-test@1.0.0');
                    
                    // Register components
                    const components = [
                        new MidiInputComponent(),
                        new MidiFilterComponent(),
                        new MidiOutputComponent()
                    ];
                    
                    components.forEach(c => {
                        this.editor.register(c);
                        this.engine.register(c);
                    });
                    
                    // Use plugins
                    this.editor.use(ConnectionPlugin.default);
                    this.editor.use(VueRenderPlugin.default);
                    this.editor.use(ContextMenuPlugin.default, {
                        'Add MIDI Input': () => this.addMidiInput(),
                        'Add MIDI Filter': () => this.addMidiFilter(),
                        'Add MIDI Output': () => this.addMidiOutput(),
                        'Delete': () => this.deleteSelected()
                    });
                    this.editor.use(AreaPlugin);
                    
                    // Event listeners
                    this.editor.on('nodecreated noderemoved connectioncreated connectionremoved', () => {
                        this.updateCounts();
                        this.engine.process(this.editor.toJSON());
                    });
                    
                    // Create initial nodes
                    await this.createSampleGraph();
                    
                    this.editor.view.resize();
                    this.editor.trigger('process');
                },
                
                async addMidiInput() {
                    const component = this.editor.components.get('MIDI Input');
                    const node = await component.createNode();
                    node.position = [100, 100 + Math.random() * 200];
                    this.editor.addNode(node);
                },
                
                async addMidiFilter() {
                    const component = this.editor.components.get('MIDI Filter');
                    const node = await component.createNode();
                    node.position = [350, 100 + Math.random() * 200];
                    this.editor.addNode(node);
                },
                
                async addMidiOutput() {
                    const component = this.editor.components.get('MIDI Output');
                    const node = await component.createNode();
                    node.position = [600, 100 + Math.random() * 200];
                    this.editor.addNode(node);
                },
                
                clearNodes() {
                    this.editor.clear();
                },
                
                deleteSelected() {
                    const selected = this.editor.selected.list;
                    selected.forEach(item => {
                        if (item instanceof Rete.Node) {
                            this.editor.removeNode(item);
                        } else if (item instanceof Rete.Connection) {
                            this.editor.removeConnection(item);
                        }
                    });
                },
                
                async createSampleGraph() {
                    // Create sample nodes
                    const input = await this.editor.components.get('MIDI Input').createNode();
                    input.position = [100, 150];
                    this.editor.addNode(input);
                    
                    const filter = await this.editor.components.get('MIDI Filter').createNode();
                    filter.position = [350, 150];
                    this.editor.addNode(filter);
                    
                    const output = await this.editor.components.get('MIDI Output').createNode();
                    output.position = [600, 150];
                    this.editor.addNode(output);
                    
                    // Connect them
                    const inputOutput = input.outputs.get('midi');
                    const filterInput = filter.inputs.get('midi');
                    const filterOutput = filter.outputs.get('midi');
                    const outputInput = output.inputs.get('midi');
                    
                    this.editor.connect(inputOutput, filterInput);
                    this.editor.connect(filterOutput, outputInput);
                },
                
                updateCounts() {
                    this.nodeCount = this.editor.nodes.length;
                    this.connectionCount = this.editor.connections.length;
                }
            }
        });
    </script>
</body>
</html>
