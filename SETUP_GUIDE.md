# MIDI Node Editor - Complete Setup Guide

## 🚀 Quick Test (No Installation Required)

**Start here to see the interface working immediately:**

1. **Open the standalone test**:
   - Double-click `standalone_test.html` in your file explorer
   - Or open it in any modern web browser
   - This shows the node editor interface with mock MIDI processing

2. **Test the interface**:
   - Drag nodes around
   - Right-click to add new nodes
   - Connect nodes by dragging from output (right) to input (left)
   - Use toolbar buttons to add/remove nodes

## 📋 Current Project Status

✅ **Completed**:
- Complete C++ backend architecture for high-performance MIDI processing
- Web-based node editor interface with Vue.js and Rete.js
- MIDI-specific node types (Input, Filter, Transform, Visualizer, Output)
- Memory-efficient data structures for millions of notes
- Spatial indexing for fast range queries
- Proprietary fast-loading format design

⏳ **Needs Setup**:
- C++ compiler and build tools
- Dependencies (Crow web framework, nlohmann/json)
- Backend compilation and testing

## 🛠️ Installation Options

### Option 1: Visual Studio Community (Recommended for Windows)

1. **Download Visual Studio Community 2022** (free):
   - Go to: https://visualstudio.microsoft.com/downloads/
   - Download "Visual Studio Community 2022"

2. **During installation, select**:
   - ✅ "Desktop development with C++"
   - ✅ "CMake tools for C++"
   - ✅ "Git for Windows" (if not already installed)

3. **Install CMake separately**:
   - Download from: https://cmake.org/download/
   - Choose "Windows x64 Installer"
   - ✅ Check "Add CMake to system PATH"

4. **Build the project**:
   ```cmd
   .\build.bat
   ```

### Option 2: MSYS2 + MinGW (Lightweight alternative)

1. **Install MSYS2**:
   - Download from: https://www.msys2.org/
   - Run installer and follow instructions

2. **Open MSYS2 terminal and install tools**:
   ```bash
   pacman -Syu  # Update package database
   pacman -S mingw-w64-x86_64-gcc mingw-w64-x86_64-cmake mingw-w64-x86_64-make
   ```

3. **Add to Windows PATH**:
   - Add `C:\msys64\mingw64\bin` to your Windows PATH environment variable
   - Restart command prompt/PowerShell

4. **Build the project**:
   ```cmd
   .\build_mingw.bat
   ```

### Option 3: Node.js Development Server (For frontend testing)

1. **Install Node.js**:
   - Download from: https://nodejs.org/
   - Choose LTS version

2. **Run development server**:
   ```cmd
   node dev_server.js
   ```

3. **Open browser**:
   - Go to: http://localhost:8080

## 🔧 Build Instructions

### After Installing Tools:

1. **Clone/Download dependencies**:
   ```cmd
   .\install_dependencies.bat
   ```

2. **Build the project**:
   ```cmd
   # Try these in order until one works:
   .\build.bat              # Auto-detects best compiler
   .\build_mingw.bat        # Specifically for MinGW
   .\build_direct.bat       # Direct compilation without CMake
   ```

3. **Run the server**:
   ```cmd
   cd build
   .\web_node_editor.exe
   ```

4. **Open in browser**:
   - Navigate to: http://localhost:8080

## 🧪 Testing the System

### Frontend Testing (No backend required):
1. Open `standalone_test.html` in your browser
2. Test node creation, connection, and interaction
3. Verify the interface works as expected

### Backend Testing:
1. After building, run the executable
2. Open http://localhost:8080/simple for basic test
3. Check console output for any errors
4. Test API endpoints with the test page

### Full System Testing:
1. Load a MIDI file through the interface
2. Apply filters and transformations
3. Visualize the results
4. Export processed MIDI

## 📁 Project Structure

```
Pattern Weaver (PW)/
├── main.cpp                 # C++ backend server
├── midi_parser.h/.cpp       # High-performance MIDI parsing
├── CMakeLists.txt           # Build configuration
├── web/
│   ├── index.html           # Main application
│   ├── simple.html          # Test page
│   └── static/
│       ├── style.css        # Styling
│       ├── components.js    # MIDI node components
│       └── app.js           # Main application logic
├── standalone_test.html     # No-backend test
├── dev_server.js           # Node.js development server
├── build_*.bat             # Build scripts
└── examples/               # Usage examples
```

## 🎯 Next Steps

1. **Choose your installation method** from the options above
2. **Test the standalone interface** to see the UI working
3. **Install build tools** for your chosen method
4. **Build and run the backend** server
5. **Test with real MIDI files** once everything is working

## 🐛 Troubleshooting

### "Command not found" errors:
- Make sure tools are installed and in your PATH
- Restart command prompt after installation
- Try different build scripts

### Build failures:
- Check that you have C++20 support
- Verify internet connection for dependency downloads
- Try the direct compilation method

### Runtime errors:
- Check that port 8080 is not in use
- Verify web files are in the correct location
- Check console output for specific error messages

## 🎵 Features Overview

Once built, the system will support:

- **Massive MIDI files**: Handle 10+ million notes efficiently
- **Real-time processing**: Instant feedback on changes
- **Visual editing**: Node-based workflow for complex operations
- **High performance**: Multi-threaded parsing and processing
- **Multiple formats**: MIDI, proprietary fast format, JSON export
- **Advanced visualization**: Piano roll, histograms, timelines

## 📞 Getting Help

If you encounter issues:
1. Check this guide first
2. Try the standalone test to isolate frontend vs backend issues
3. Check the console output for specific error messages
4. Try different build methods if one fails
