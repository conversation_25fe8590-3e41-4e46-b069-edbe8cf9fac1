#include <crow.h>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <fstream>
#include <sstream>
#include <iostream>

using json = nlohmann::json;

struct Node {
    int id;
    std::string type;
    std::string name;
    double x;
    double y;
    json data;
    std::vector<int> inputs;
    std::vector<int> outputs;

    json toJson() const {
        json j;
        j["id"] = id;
        j["type"] = type;
        j["name"] = name;
        j["x"] = x;
        j["y"] = y;
        j["data"] = data;
        j["inputs"] = inputs;
        j["outputs"] = outputs;
        return j;
    }

    static Node fromJson(const json& j) {
        Node node;
        node.id = j.value("id", 0);
        node.type = j.value("type", "");
        node.name = j.value("name", "");
        node.x = j.value("x", 0.0);
        node.y = j.value("y", 0.0);
        node.data = j.value("data", json::object());
        node.inputs = j.value("inputs", std::vector<int>());
        node.outputs = j.value("outputs", std::vector<int>());
        return node;
    }
};

struct Connection {
    int id;
    int sourceNodeId;
    int sourcePortId;
    int targetNodeId;
    int targetPortId;

    json toJson() const {
        json j;
        j["id"] = id;
        j["sourceNodeId"] = sourceNodeId;
        j["sourcePortId"] = sourcePortId;
        j["targetNodeId"] = targetNodeId;
        j["targetPortId"] = targetPortId;
        return j;
    }

    static Connection fromJson(const json& j) {
        Connection conn;
        conn.id = j.value("id", 0);
        conn.sourceNodeId = j.value("sourceNodeId", 0);
        conn.sourcePortId = j.value("sourcePortId", 0);
        conn.targetNodeId = j.value("targetNodeId", 0);
        conn.targetPortId = j.value("targetPortId", 0);
        return conn;
    }
};

class NodeGraph {
private:
    std::unordered_map<int, Node> nodes;
    std::unordered_map<int, Connection> connections;
    int nextNodeId = 1;
    int nextConnectionId = 1;

public:
    json getGraph() {
        json j;
        j["nodes"] = json::array();
        j["connections"] = json::array();

        for (const auto& [id, node] : nodes) {
            j["nodes"].push_back(node.toJson());
        }

        for (const auto& [id, conn] : connections) {
            j["connections"].push_back(conn.toJson());
        }

        return j;
    }

    int addNode(const json& nodeData) {
        Node node = Node::fromJson(nodeData);
        if (node.id == 0) {
            node.id = nextNodeId++;
        } else {
            nextNodeId = std::max(nextNodeId, node.id + 1);
        }

        nodes[node.id] = node;
        return node.id;
    }

    int addConnection(const json& connData) {
        Connection conn = Connection::fromJson(connData);
        if (conn.id == 0) {
            conn.id = nextConnectionId++;
        } else {
            nextConnectionId = std::max(nextConnectionId, conn.id + 1);
        }

        connections[conn.id] = conn;
        return conn.id;
    }

    bool updateNode(int id, const json& nodeData) {
        if (nodes.find(id) == nodes.end())
            return false;

        Node node = Node::fromJson(nodeData);
        node.id = id;
        nodes[id] = node;
        return true;
    }

    bool removeNode(int id) {
        if (nodes.find(id) == nodes.end())
            return false;

        nodes.erase(id);

        // Remove associated connections
        auto it = connections.begin();
        while (it != connections.end()) {
            if (it->second.sourceNodeId == id || it->second.targetNodeId == id)
                it = connections.erase(it);
            else
                ++it;
        }

        return true;
    }

    bool removeConnection(int id) {
        if (connections.find(id) == connections.end())
            return false;

        connections.erase(id);
        return true;
    }

    void clear() {
        nodes.clear();
        connections.clear();
        nextNodeId = 1;
        nextConnectionId = 1;
    }

    bool saveToFile(const std::string& filename) {
        try {
            std::ofstream file(filename);
            file << getGraph().dump(4);
            return true;
        } catch (...) {
            return false;
        }
    }

    bool loadFromFile(const std::string& filename) {
        try {
            std::ifstream file(filename);
            json j;
            file >> j;

            clear();

            if (j.contains("nodes")) {
                for (const auto& nodeJson : j["nodes"]) {
                    addNode(nodeJson);
                }
            }

            if (j.contains("connections")) {
                for (const auto& connJson : j["connections"]) {
                    addConnection(connJson);
                }
            }

            return true;
        } catch (...) {
            return false;
        }
    }
};

std::string readFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return "";
    }
    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

int main() {
    crow::SimpleApp app;
    NodeGraph graph;

    // Serve the main HTML page
    CROW_ROUTE(app, "/")
        .response([](){
            std::string content = readFile("web/index.html");
            if (content.empty()) {
                return crow::response(404, "index.html not found");
            }
            return crow::response(200, "text/html", content);
        });

    // Serve static files
    CROW_ROUTE(app, "/static/<path>")
        ([](const std::string& path){
            std::string fullPath = "web/static/" + path;
            std::string content = readFile(fullPath);

            if (content.empty()) {
                return crow::response(404, "File not found");
            }

            std::string contentType = "text/plain";
            if (path.ends_with(".css")) contentType = "text/css";
            else if (path.ends_with(".js")) contentType = "application/javascript";
            else if (path.ends_with(".html")) contentType = "text/html";

            return crow::response(200, contentType, content);
        });

    // API endpoints
    CROW_ROUTE(app, "/api/graph")
        .methods("GET"_method)
        ([&graph](){
            auto response = crow::response(200, "application/json", graph.getGraph().dump());
            response.add_header("Access-Control-Allow-Origin", "*");
            return response;
        });

    CROW_ROUTE(app, "/api/nodes")
        .methods("POST"_method)
        ([&graph](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                int id = graph.addNode(data);
                return crow::response(200, "application/json", json({{"id", id}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid JSON"}}).dump());
            }
        });

    CROW_ROUTE(app, "/api/nodes/<int>")
        .methods("PUT"_method)
        ([&graph](const crow::request& req, int id){
            try {
                auto data = json::parse(req.body);
                bool success = graph.updateNode(id, data);
                return crow::response(success ? 200 : 404, "application/json",
                    json({{"success", success}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid JSON"}}).dump());
            }
        });

    CROW_ROUTE(app, "/api/nodes/<int>")
        .methods("DELETE"_method)
        ([&graph](int id){
            bool success = graph.removeNode(id);
            return crow::response(success ? 200 : 404, "application/json",
                json({{"success", success}}).dump());
        });

    CROW_ROUTE(app, "/api/connections")
        .methods("POST"_method)
        ([&graph](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                int id = graph.addConnection(data);
                return crow::response(200, "application/json", json({{"id", id}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid JSON"}}).dump());
            }
        });

    CROW_ROUTE(app, "/api/connections/<int>")
        .methods("DELETE"_method)
        ([&graph](int id){
            bool success = graph.removeConnection(id);
            return crow::response(success ? 200 : 404, "application/json",
                json({{"success", success}}).dump());
        });

    CROW_ROUTE(app, "/api/save")
        .methods("POST"_method)
        ([&graph](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                std::string filename = data.value("filename", "graph.json");
                bool success = graph.saveToFile("saves/" + filename);
                return crow::response(success ? 200 : 500, "application/json",
                    json({{"success", success}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid request"}}).dump());
            }
        });

    CROW_ROUTE(app, "/api/load")
        .methods("POST"_method)
        ([&graph](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                std::string filename = data.value("filename", "graph.json");
                bool success = graph.loadFromFile("saves/" + filename);
                return crow::response(success ? 200 : 500, "application/json",
                    json({{"success", success}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid request"}}).dump());
            }
        });

    std::cout << "Starting web node editor server on http://localhost:8080" << std::endl;
    app.port(8080).multithreaded().run();
    return 0;
}