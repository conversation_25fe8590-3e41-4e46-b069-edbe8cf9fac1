#include <crow.h>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <fstream>
#include <sstream>
#include <iostream>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <algorithm>
#include <execution>
#include "midi_parser.h"

using json = nlohmann::json;

// High-performance MIDI data structures
struct MidiNote {
    uint32_t start_tick;
    uint32_t end_tick;
    uint8_t channel;
    uint8_t note;
    uint8_t velocity;
    uint16_t track_id;

    // For fast sorting and searching
    bool operator<(const MidiNote& other) const {
        return start_tick < other.start_tick;
    }
};

struct MidiEvent {
    uint32_t tick;
    uint8_t type;
    uint8_t channel;
    std::vector<uint8_t> data;
    uint16_t track_id;
};

// Memory-efficient storage for millions of notes
class MidiDataStore {
private:
    std::vector<MidiNote> notes;
    std::vector<MidiEvent> events;
    std::mutex data_mutex;
    std::atomic<bool> is_loading{false};

    // Spatial indexing for fast range queries
    struct TimeIndex {
        uint32_t start_tick;
        uint32_t end_tick;
        size_t note_index;
    };
    std::vector<TimeIndex> time_index;

public:
    void reserve_capacity(size_t note_count, size_t event_count) {
        std::lock_guard<std::mutex> lock(data_mutex);
        notes.reserve(note_count);
        events.reserve(event_count);
        time_index.reserve(note_count);
    }

    void add_note(const MidiNote& note) {
        std::lock_guard<std::mutex> lock(data_mutex);
        notes.push_back(note);
        time_index.push_back({note.start_tick, note.end_tick, notes.size() - 1});
    }

    void add_event(const MidiEvent& event) {
        std::lock_guard<std::mutex> lock(data_mutex);
        events.push_back(event);
    }

    void optimize_for_playback() {
        std::lock_guard<std::mutex> lock(data_mutex);

        // Sort notes by start time for efficient playback
        std::sort(std::execution::par_unseq, notes.begin(), notes.end());

        // Rebuild time index
        time_index.clear();
        for (size_t i = 0; i < notes.size(); ++i) {
            time_index.push_back({notes[i].start_tick, notes[i].end_tick, i});
        }
        std::sort(std::execution::par_unseq, time_index.begin(), time_index.end(),
                  [](const TimeIndex& a, const TimeIndex& b) {
                      return a.start_tick < b.start_tick;
                  });
    }

    std::vector<MidiNote> get_notes_in_range(uint32_t start_tick, uint32_t end_tick) const {
        std::lock_guard<std::mutex> lock(data_mutex);
        std::vector<MidiNote> result;

        // Binary search for efficient range queries
        auto start_it = std::lower_bound(time_index.begin(), time_index.end(), start_tick,
                                        [](const TimeIndex& idx, uint32_t tick) {
                                            return idx.end_tick < tick;
                                        });

        auto end_it = std::upper_bound(time_index.begin(), time_index.end(), end_tick,
                                      [](uint32_t tick, const TimeIndex& idx) {
                                          return tick < idx.start_tick;
                                      });

        for (auto it = start_it; it != end_it; ++it) {
            result.push_back(notes[it->note_index]);
        }

        return result;
    }

    size_t get_note_count() const {
        std::lock_guard<std::mutex> lock(data_mutex);
        return notes.size();
    }

    void clear() {
        std::lock_guard<std::mutex> lock(data_mutex);
        notes.clear();
        events.clear();
        time_index.clear();
    }

    json to_json_range(uint32_t start_tick, uint32_t end_tick, size_t max_notes = 10000) const {
        auto range_notes = get_notes_in_range(start_tick, end_tick);

        // Limit the number of notes sent to frontend for performance
        if (range_notes.size() > max_notes) {
            // Sample notes evenly across the range
            std::vector<MidiNote> sampled;
            sampled.reserve(max_notes);

            double step = static_cast<double>(range_notes.size()) / max_notes;
            for (size_t i = 0; i < max_notes; ++i) {
                size_t idx = static_cast<size_t>(i * step);
                if (idx < range_notes.size()) {
                    sampled.push_back(range_notes[idx]);
                }
            }
            range_notes = std::move(sampled);
        }

        json j;
        j["notes"] = json::array();
        j["total_notes"] = get_note_count();
        j["displayed_notes"] = range_notes.size();
        j["start_tick"] = start_tick;
        j["end_tick"] = end_tick;

        for (const auto& note : range_notes) {
            json note_json;
            note_json["start"] = note.start_tick;
            note_json["end"] = note.end_tick;
            note_json["channel"] = note.channel;
            note_json["note"] = note.note;
            note_json["velocity"] = note.velocity;
            note_json["track"] = note.track_id;
            j["notes"].push_back(note_json);
        }

        return j;
    }
};

struct Node {
    int id;
    std::string type;
    std::string name;
    double x;
    double y;
    json data;
    std::vector<int> inputs;
    std::vector<int> outputs;

    json toJson() const {
        json j;
        j["id"] = id;
        j["type"] = type;
        j["name"] = name;
        j["x"] = x;
        j["y"] = y;
        j["data"] = data;
        j["inputs"] = inputs;
        j["outputs"] = outputs;
        return j;
    }

    static Node fromJson(const json& j) {
        Node node;
        node.id = j.value("id", 0);
        node.type = j.value("type", "");
        node.name = j.value("name", "");
        node.x = j.value("x", 0.0);
        node.y = j.value("y", 0.0);
        node.data = j.value("data", json::object());
        node.inputs = j.value("inputs", std::vector<int>());
        node.outputs = j.value("outputs", std::vector<int>());
        return node;
    }
};

struct Connection {
    int id;
    int sourceNodeId;
    int sourcePortId;
    int targetNodeId;
    int targetPortId;

    json toJson() const {
        json j;
        j["id"] = id;
        j["sourceNodeId"] = sourceNodeId;
        j["sourcePortId"] = sourcePortId;
        j["targetNodeId"] = targetNodeId;
        j["targetPortId"] = targetPortId;
        return j;
    }

    static Connection fromJson(const json& j) {
        Connection conn;
        conn.id = j.value("id", 0);
        conn.sourceNodeId = j.value("sourceNodeId", 0);
        conn.sourcePortId = j.value("sourcePortId", 0);
        conn.targetNodeId = j.value("targetNodeId", 0);
        conn.targetPortId = j.value("targetPortId", 0);
        return conn;
    }
};

class NodeGraph {
private:
    std::unordered_map<int, Node> nodes;
    std::unordered_map<int, Connection> connections;
    int nextNodeId = 1;
    int nextConnectionId = 1;

public:
    json getGraph() {
        json j;
        j["nodes"] = json::array();
        j["connections"] = json::array();

        for (const auto& [id, node] : nodes) {
            j["nodes"].push_back(node.toJson());
        }

        for (const auto& [id, conn] : connections) {
            j["connections"].push_back(conn.toJson());
        }

        return j;
    }

    int addNode(const json& nodeData) {
        Node node = Node::fromJson(nodeData);
        if (node.id == 0) {
            node.id = nextNodeId++;
        } else {
            nextNodeId = std::max(nextNodeId, node.id + 1);
        }

        nodes[node.id] = node;
        return node.id;
    }

    int addConnection(const json& connData) {
        Connection conn = Connection::fromJson(connData);
        if (conn.id == 0) {
            conn.id = nextConnectionId++;
        } else {
            nextConnectionId = std::max(nextConnectionId, conn.id + 1);
        }

        connections[conn.id] = conn;
        return conn.id;
    }

    bool updateNode(int id, const json& nodeData) {
        if (nodes.find(id) == nodes.end())
            return false;

        Node node = Node::fromJson(nodeData);
        node.id = id;
        nodes[id] = node;
        return true;
    }

    bool removeNode(int id) {
        if (nodes.find(id) == nodes.end())
            return false;

        nodes.erase(id);

        // Remove associated connections
        auto it = connections.begin();
        while (it != connections.end()) {
            if (it->second.sourceNodeId == id || it->second.targetNodeId == id)
                it = connections.erase(it);
            else
                ++it;
        }

        return true;
    }

    bool removeConnection(int id) {
        if (connections.find(id) == connections.end())
            return false;

        connections.erase(id);
        return true;
    }

    void clear() {
        nodes.clear();
        connections.clear();
        nextNodeId = 1;
        nextConnectionId = 1;
    }

    bool saveToFile(const std::string& filename) {
        try {
            std::ofstream file(filename);
            file << getGraph().dump(4);
            return true;
        } catch (...) {
            return false;
        }
    }

    bool loadFromFile(const std::string& filename) {
        try {
            std::ifstream file(filename);
            json j;
            file >> j;

            clear();

            if (j.contains("nodes")) {
                for (const auto& nodeJson : j["nodes"]) {
                    addNode(nodeJson);
                }
            }

            if (j.contains("connections")) {
                for (const auto& connJson : j["connections"]) {
                    addConnection(connJson);
                }
            }

            return true;
        } catch (...) {
            return false;
        }
    }
};

std::string readFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return "";
    }
    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

int main() {
    crow::SimpleApp app;
    NodeGraph graph;
    MidiDataStore midi_store;

    // Serve the main HTML page
    CROW_ROUTE(app, "/")
        .response([](){
            std::string content = readFile("web/index.html");
            if (content.empty()) {
                return crow::response(404, "index.html not found");
            }
            return crow::response(200, "text/html", content);
        });

    // Serve static files
    CROW_ROUTE(app, "/static/<path>")
        ([](const std::string& path){
            std::string fullPath = "web/static/" + path;
            std::string content = readFile(fullPath);

            if (content.empty()) {
                return crow::response(404, "File not found");
            }

            std::string contentType = "text/plain";
            if (path.ends_with(".css")) contentType = "text/css";
            else if (path.ends_with(".js")) contentType = "application/javascript";
            else if (path.ends_with(".html")) contentType = "text/html";

            return crow::response(200, contentType, content);
        });

    // API endpoints
    CROW_ROUTE(app, "/api/graph")
        .methods("GET"_method)
        ([&graph](){
            auto response = crow::response(200, "application/json", graph.getGraph().dump());
            response.add_header("Access-Control-Allow-Origin", "*");
            return response;
        });

    CROW_ROUTE(app, "/api/nodes")
        .methods("POST"_method)
        ([&graph](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                int id = graph.addNode(data);
                return crow::response(200, "application/json", json({{"id", id}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid JSON"}}).dump());
            }
        });

    CROW_ROUTE(app, "/api/nodes/<int>")
        .methods("PUT"_method)
        ([&graph](const crow::request& req, int id){
            try {
                auto data = json::parse(req.body);
                bool success = graph.updateNode(id, data);
                return crow::response(success ? 200 : 404, "application/json",
                    json({{"success", success}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid JSON"}}).dump());
            }
        });

    CROW_ROUTE(app, "/api/nodes/<int>")
        .methods("DELETE"_method)
        ([&graph](int id){
            bool success = graph.removeNode(id);
            return crow::response(success ? 200 : 404, "application/json",
                json({{"success", success}}).dump());
        });

    CROW_ROUTE(app, "/api/connections")
        .methods("POST"_method)
        ([&graph](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                int id = graph.addConnection(data);
                return crow::response(200, "application/json", json({{"id", id}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid JSON"}}).dump());
            }
        });

    CROW_ROUTE(app, "/api/connections/<int>")
        .methods("DELETE"_method)
        ([&graph](int id){
            bool success = graph.removeConnection(id);
            return crow::response(success ? 200 : 404, "application/json",
                json({{"success", success}}).dump());
        });

    CROW_ROUTE(app, "/api/save")
        .methods("POST"_method)
        ([&graph](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                std::string filename = data.value("filename", "graph.json");
                bool success = graph.saveToFile("saves/" + filename);
                return crow::response(success ? 200 : 500, "application/json",
                    json({{"success", success}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid request"}}).dump());
            }
        });

    CROW_ROUTE(app, "/api/load")
        .methods("POST"_method)
        ([&graph](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                std::string filename = data.value("filename", "graph.json");
                bool success = graph.loadFromFile("saves/" + filename);
                return crow::response(success ? 200 : 500, "application/json",
                    json({{"success", success}}).dump());
            } catch (...) {
                return crow::response(400, "application/json", json({{"error", "Invalid request"}}).dump());
            }
        });

    // MIDI-specific API endpoints
    CROW_ROUTE(app, "/api/upload-midi")
        .methods("POST"_method)
        ([&midi_store](const crow::request& req){
            try {
                // Handle file upload (simplified - in real implementation, use multipart parser)
                std::string filename = "uploaded.mid"; // Extract from multipart data

                // Parse MIDI file
                FastMidiParser parser;
                auto result = parser.parse_file("uploads/" + filename);

                if (result.success) {
                    // Store in memory for fast access
                    midi_store.clear();
                    midi_store.reserve_capacity(result.notes.size(), result.events.size());

                    for (const auto& note : result.notes) {
                        midi_store.add_note(note);
                    }

                    midi_store.optimize_for_playback();

                    json response;
                    response["success"] = true;
                    response["note_count"] = result.notes.size();
                    response["parse_time_ms"] = result.parse_time_ms;
                    response["filename"] = filename;

                    auto resp = crow::response(200, "application/json", response.dump());
                    resp.add_header("Access-Control-Allow-Origin", "*");
                    return resp;
                } else {
                    json response;
                    response["success"] = false;
                    response["error"] = result.error_message;

                    auto resp = crow::response(400, "application/json", response.dump());
                    resp.add_header("Access-Control-Allow-Origin", "*");
                    return resp;
                }
            } catch (...) {
                json response;
                response["success"] = false;
                response["error"] = "Failed to process MIDI file";

                auto resp = crow::response(500, "application/json", response.dump());
                resp.add_header("Access-Control-Allow-Origin", "*");
                return resp;
            }
        });

    CROW_ROUTE(app, "/api/midi-range")
        .methods("GET"_method)
        ([&midi_store](const crow::request& req){
            try {
                uint32_t start_tick = std::stoul(req.url_params.get("start") ?: "0");
                uint32_t end_tick = std::stoul(req.url_params.get("end") ?: "1000");
                size_t max_notes = std::stoul(req.url_params.get("max") ?: "10000");

                json response = midi_store.to_json_range(start_tick, end_tick, max_notes);

                auto resp = crow::response(200, "application/json", response.dump());
                resp.add_header("Access-Control-Allow-Origin", "*");
                return resp;
            } catch (...) {
                json response;
                response["error"] = "Invalid parameters";

                auto resp = crow::response(400, "application/json", response.dump());
                resp.add_header("Access-Control-Allow-Origin", "*");
                return resp;
            }
        });

    CROW_ROUTE(app, "/api/midi-stats")
        .methods("GET"_method)
        ([&midi_store](){
            json response;
            response["total_notes"] = midi_store.get_note_count();
            response["memory_usage_mb"] = (midi_store.get_note_count() * sizeof(MidiNote)) / (1024 * 1024);

            auto resp = crow::response(200, "application/json", response.dump());
            resp.add_header("Access-Control-Allow-Origin", "*");
            return resp;
        });

    CROW_ROUTE(app, "/api/export-midi")
        .methods("POST"_method)
        ([&midi_store](const crow::request& req){
            try {
                auto data = json::parse(req.body);
                std::string format = data.value("format", "midi");
                std::string filename = data.value("filename", "export.mid");

                // Export logic would go here
                json response;
                response["success"] = true;
                response["filename"] = filename;
                response["format"] = format;

                auto resp = crow::response(200, "application/json", response.dump());
                resp.add_header("Access-Control-Allow-Origin", "*");
                return resp;
            } catch (...) {
                json response;
                response["success"] = false;
                response["error"] = "Export failed";

                auto resp = crow::response(500, "application/json", response.dump());
                resp.add_header("Access-Control-Allow-Origin", "*");
                return resp;
            }
        });

    std::cout << "Starting MIDI Node Editor server on http://localhost:8080" << std::endl;
    std::cout << "Features:" << std::endl;
    std::cout << "- High-performance MIDI parsing for millions of notes" << std::endl;
    std::cout << "- Real-time visualization and processing" << std::endl;
    std::cout << "- Node-based editing interface" << std::endl;
    std::cout << "- Fast proprietary format for instant loading" << std::endl;

    app.port(8080).multithreaded().run();
    return 0;
}