# WEAVE File Format Specification

## Overview
WEAVE (.weave) is the proprietary file format for Pattern Weaver MIDI Node Editor projects. It stores the complete state of a node-based MIDI editing session, including nodes, connections, MIDI data, and project metadata.

## File Structure
WEAVE files are JSON-based with the following structure:

```json
{
  "format": "WEAVE",
  "version": "1.0",
  "timestamp": "2025-06-21T14:30:00.000Z",
  "metadata": {
    "name": "Project Name",
    "description": "Project Description",
    "author": "Author Name",
    "created": "2025-06-21T14:30:00.000Z",
    "modified": "2025-06-21T14:30:00.000Z",
    "tags": ["tag1", "tag2"]
  },
  "project": {
    "nodes": [...],
    "connections": [...],
    "midi_data": {...},
    "settings": {...}
  }
}
```

## Node Structure
```json
{
  "id": "unique-node-id",
  "type": "piano_roll|midi_input|midi_output|filter|transform|visualizer",
  "name": "Node Display Name",
  "position": {"x": 100, "y": 200},
  "data": {
    "type-specific": "properties"
  },
  "inputs": [...],
  "outputs": [...]
}
```

## Connection Structure
```json
{
  "id": "unique-connection-id",
  "source": {
    "node_id": "source-node-id",
    "port": "output-port-name"
  },
  "target": {
    "node_id": "target-node-id",
    "port": "input-port-name"
  }
}
```

## MIDI Data Structure
```json
{
  "tracks": [
    {
      "id": "track-id",
      "name": "Track Name",
      "channel": 0,
      "notes": [
        {
          "start": 0,
          "end": 480,
          "note": 60,
          "velocity": 100
        }
      ],
      "events": [...]
    }
  ],
  "tempo": 120,
  "time_signature": [4, 4],
  "ticks_per_quarter": 480
}
```

## Node Type Specifications

### Piano Roll Node
```json
{
  "type": "piano_roll",
  "data": {
    "zoom": 1.0,
    "scroll_x": 0,
    "scroll_y": 60,
    "snap_to_grid": true,
    "grid_size": 120,
    "notes": [...]
  }
}
```

### MIDI Input Node
```json
{
  "type": "midi_input",
  "data": {
    "source_file": "path/to/file.mid",
    "loaded_notes": 1000,
    "parse_time_ms": 15.5
  }
}
```

### MIDI Output Node
```json
{
  "type": "midi_output",
  "data": {
    "output_device": "Microsoft GS Wavetable Synth",
    "channel_mapping": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
  }
}
```

## File Operations

### Saving
1. Serialize current node graph
2. Extract MIDI data from all nodes
3. Compress large datasets
4. Write to .weave file

### Loading
1. Parse JSON structure
2. Validate format version
3. Recreate nodes and connections
4. Load MIDI data into memory
5. Restore UI state

## Compression
Large MIDI datasets are compressed using:
- Note data: Delta compression for timestamps
- Velocity data: Run-length encoding
- Channel data: Bit packing

## Version Compatibility
- v1.0: Initial format
- Future versions will maintain backward compatibility
